/**
 * 增强的响应格式化工具
 * 负责将各种服务的响应格式化为用户友好的文本
 * 支持多语言、智能截断、相关性排序和视觉优化
 */

import { AxmolResource, CodeExample, ApiReference, BuildIssue, MigrationGuide, PlatformInfo, CodeAnalysisResult, VersionComparison, BestPractice, CommunitySolution } from '../types/index.js';

// 格式化选项
interface FormatOptions {
  language?: 'zh' | 'en'; // 输出语言
  maxLength?: number; // 最大长度限制
  includeMetadata?: boolean; // 是否包含元数据
  sortByRelevance?: boolean; // 是否按相关性排序
  compact?: boolean; // 紧凑模式
  visualEnhancement?: boolean; // 视觉增强
}

// 国际化文本
const i18n = {
  zh: {
    noResults: '❌ 未找到相关内容',
    searchResults: '📚 **Axmol 搜索结果**',
    codeExamples: '💻 **Axmol 代码示例**',
    apiReference: '📖 **Axmol API 参考**',
    buildDiagnostic: '🔧 **Axmol 构建问题诊断**',
    migrationGuide: '🔄 **迁移指南**',
    platformInfo: '🎯 **平台信息**',
    codeAnalysis: '🔍 **Axmol 代码分析结果**',
    versionComparison: '🔄 **Axmol 版本对比**',
    bestPractices: '💡 **Axmol 最佳实践**',
    communitySolution: '🌐 **社区解决方案**',
    type: '**类型**',
    source: '**来源**',
    relevance: '**相关性**',
    link: '**链接**',
    summary: '**摘要**',
    matchedTerms: '**匹配关键词**',
    language: '**语言**',
    platform: '**平台**',
    description: '**描述**',
    sourceCode: '**源码链接**',
    codeExample: '**代码示例**',
    className: '**类名**',
    namespace: '**命名空间**',
    sourceFile: '**源文件**',
    docLink: '**文档链接**',
    method: '方法',
    returnType: '**返回类型**',
    parameters: '**参数**',
    optional: '(可选)',
    defaultValue: '默认值',
    usageExamples: '使用示例',
    relatedApis: '相关API',
    errorType: '**错误类型**',
    errorMessage: '**错误信息**',
    solutions: '解决方案',
    priority: '**优先级**',
    verified: '**验证状态**',
    steps: '**解决步骤**',
    codeChanges: '**代码修改**',
    configChanges: '**配置修改**',
    relatedIssues: '相关问题',
    apiChanges: 'API 变更',
    commonIssues: '常见问题',
    systemRequirements: '系统要求',
    configSettings: '配置设置',
    buildSteps: '构建步骤',
    configExamples: '配置示例',
    foundIssues: '发现的问题',
    suggestions: '改进建议',
    bestPracticesSection: '相关最佳实践',
    performanceOptimization: '性能优化建议',
    securityCheck: '安全检查',
    versionChanges: '版本变更',
    compatibilityInfo: '兼容性信息',
    migrationNotes: '迁移说明',
    recommendations: '建议',
    antiPatterns: '应避免的做法',
    performanceImpact: '性能影响',
    relatedDiscussions: '相关讨论',
    relatedIssuesSection: '相关 Issues'
  },
  en: {
    noResults: '❌ No relevant content found',
    searchResults: '📚 **Axmol Search Results**',
    codeExamples: '💻 **Axmol Code Examples**',
    apiReference: '📖 **Axmol API Reference**',
    buildDiagnostic: '🔧 **Axmol Build Issue Diagnostic**',
    migrationGuide: '🔄 **Migration Guide**',
    platformInfo: '🎯 **Platform Information**',
    codeAnalysis: '🔍 **Axmol Code Analysis Results**',
    versionComparison: '🔄 **Axmol Version Comparison**',
    bestPractices: '💡 **Axmol Best Practices**',
    communitySolution: '🌐 **Community Solutions**',
    type: '**Type**',
    source: '**Source**',
    relevance: '**Relevance**',
    link: '**Link**',
    summary: '**Summary**',
    matchedTerms: '**Matched Terms**',
    language: '**Language**',
    platform: '**Platform**',
    description: '**Description**',
    sourceCode: '**Source Code**',
    codeExample: '**Code Example**'
    // ... 其他英文翻译可以按需添加
  }
};

// 智能文本截断
function smartTruncate(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) return text;
  
  // 尝试在句子边界截断
  const sentences = text.split(/[.!?。！？]/);
  let result = '';
  
  for (const sentence of sentences) {
    if ((result + sentence).length > maxLength - suffix.length) {
      break;
    }
    result += sentence + (sentence.match(/[.!?。！？]/) ? '' : '.');
  }
  
  if (result.length === 0) {
    // 如果没有合适的句子边界，则在单词边界截断
    const words = text.split(/\s+/);
    for (const word of words) {
      if ((result + ' ' + word).length > maxLength - suffix.length) {
        break;
      }
      result += (result ? ' ' : '') + word;
    }
  }
  
  return result + suffix;
}

// 相关性评分
function calculateRelevanceScore(item: any, query?: string): number {
  if (!query) return item.relevanceScore || 0;
  
  let score = item.relevanceScore || 0;
  const queryLower = query.toLowerCase();
  
  // 标题匹配加分
  if (item.title && item.title.toLowerCase().includes(queryLower)) {
    score += 0.3;
  }
  
  // 描述匹配加分
  if (item.description && item.description.toLowerCase().includes(queryLower)) {
    score += 0.2;
  }
  
  return Math.min(score, 10);
}

// 按相关性排序
function sortByRelevance<T extends any>(items: T[], query?: string): T[] {
  return items.sort((a, b) => {
    const scoreA = calculateRelevanceScore(a, query);
    const scoreB = calculateRelevanceScore(b, query);
    return scoreB - scoreA;
  });
}

/**
 * 增强的文档搜索响应格式化
 */
export function formatDocumentationResponse(
  data: AxmolResource[], 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 2000,
    includeMetadata: true,
    sortByRelevance: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data || data.length === 0) {
    return `${t.noResults}。请尝试使用不同的关键词搜索。`;
  }

  // 按相关性排序
  const sortedData = opts.sortByRelevance ? 
    sortByRelevance(data, metadata?.query) : data;
  
  // 限制显示数量（根据紧凑模式）
  const displayLimit = opts.compact ? 5 : 10;
  const displayData = sortedData.slice(0, displayLimit);
  
  let response = `${t.searchResults} (${data.length} 个结果)`;
  
  // 添加搜索元数据
  if (opts.includeMetadata && metadata) {
    response += `\n🔍 **搜索耗时**: ${metadata.searchTime || 0}ms`;
    if (metadata.sources) {
      response += ` | **数据源**: ${metadata.sources.join(', ')}`;
    }
    if (metadata.cacheHit) {
      response += ` | 📦 **缓存命中**`;
    }
  }
  
  response += '\n\n';

  displayData.forEach((resource, index) => {
    const relevanceBar = generateRelevanceBar(resource.relevanceScore || 0);
    
    if (opts.visualEnhancement) {
      response += `### ${getResourceIcon(resource.type)} ${index + 1}. ${resource.title}\n`;
    } else {
      response += `### ${index + 1}. ${resource.title}\n`;
    }
    
    response += `${t.type}: ${getResourceTypeLabel(resource.type)}\n`;
    response += `${t.source}: ${resource.source}\n`;
    response += `${t.relevance}: ${resource.relevanceScore}/10 ${relevanceBar}\n`;
    response += `${t.link}: ${resource.url}\n`;
    
    if (resource.content) {
      const maxContentLength = opts.compact ? 150 : 300;
      const truncatedContent = smartTruncate(resource.content, maxContentLength);
      response += `${t.summary}: ${truncatedContent}\n`;
    }
    
    if (resource.matchedTerms && resource.matchedTerms.length > 0) {
      const terms = resource.matchedTerms.slice(0, 5).join(', ');
      response += `${t.matchedTerms}: ${terms}\n`;
    }
    
    response += '\n';
  });
  
  // 如果有更多结果被截断
  if (data.length > displayLimit) {
    response += `💡 显示了前 ${displayLimit} 个最相关的结果，共 ${data.length} 个结果可用。\n`;
  }
  
  // 智能截断整个响应
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 生成相关性进度条
 */
function generateRelevanceBar(score: number): string {
  const normalizedScore = Math.max(0, Math.min(10, score));
  const filledBars = Math.round(normalizedScore / 2);
  const emptyBars = 5 - filledBars;
  return '█'.repeat(filledBars) + '░'.repeat(emptyBars);
}

/**
 * 获取资源类型图标
 */
function getResourceIcon(type: string): string {
  const icons: { [key: string]: string } = {
    'official_docs': '📘',
    'wiki': '📖',
    'source': '💻',
    'example': '📝',
    'header': '📄',
    'document': '📋',
    'web': '🌐',
    'community': '👥'
  };
  return icons[type] || '📄';
}

/**
 * 增强的代码示例响应格式化
 */
export function formatCodeExamplesResponse(
  data: CodeExample[], 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 3000,
    includeMetadata: true,
    sortByRelevance: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data || data.length === 0) {
    return `${t.noResults}。请尝试使用不同的功能关键词搜索。`;
  }

  // 按相关性和质量排序
  const sortedData = opts.sortByRelevance ? 
    sortByRelevance(data, metadata?.query).sort((a, b) => {
      // 二次排序：按代码质量
      const qualityA = (a as any).qualityScore || 0;
      const qualityB = (b as any).qualityScore || 0;
      return qualityB - qualityA;
    }) : data;
  
  const displayLimit = opts.compact ? 3 : 8;
  const displayData = sortedData.slice(0, displayLimit);
  
  let response = `${t.codeExamples} (${data.length} 个示例)`;
  
  // 添加元数据
  if (opts.includeMetadata && metadata) {
    response += `\n🔍 **搜索耗时**: ${metadata.searchTime || 0}ms`;
    if (metadata.sources) {
      response += ` | **数据源**: ${metadata.sources.join(', ')}`;
    }
  }
  
  response += '\n\n';

  displayData.forEach((example, index) => {
    const langIcon = getLanguageIcon(example.language);
    const platformIcon = getPlatformIcon(example.platform);
    
    if (opts.visualEnhancement) {
      response += `### ${langIcon} ${index + 1}. ${example.title}\n`;
    } else {
      response += `### ${index + 1}. ${example.title}\n`;
    }
    
    response += `${t.language}: ${example.language.toUpperCase()} ${langIcon}\n`;
    response += `${t.platform}: ${example.platform || '通用'} ${platformIcon}\n`;
    
    // 智能截断描述
    const maxDescLength = opts.compact ? 100 : 200;
    const description = smartTruncate(example.description, maxDescLength);
    response += `${t.description}: ${description}\n`;
    
    if (example.sourceUrl) {
      response += `${t.sourceCode}: ${example.sourceUrl}\n`;
    }
    
    // 显示代码质量评分（如果有）
    if ((example as any).qualityScore) {
      const qualityBar = generateQualityBar((example as any).qualityScore);
      response += `**代码质量**: ${(example as any).qualityScore}/10 ${qualityBar}\n`;
    }
    
    response += `\n${t.codeExample}:\n`;
    
    // 智能截断代码
    const maxCodeLength = opts.compact ? 500 : 1000;
    const code = example.code.length > maxCodeLength ? 
      smartTruncate(example.code, maxCodeLength, '\n\n// ... 代码已截断') : 
      example.code;
    
    response += `\`\`\`${example.language}\n${code}\n\`\`\`\n\n`;
  });
  
  // 截断提示
  if (data.length > displayLimit) {
    response += `💡 显示了前 ${displayLimit} 个最佳示例，共 ${data.length} 个示例可用。\n`;
  }
  
  // 智能截断整个响应
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 获取编程语言图标
 */
function getLanguageIcon(language: string): string {
  const icons: { [key: string]: string } = {
    'cpp': '⚡',
    'c++': '⚡',
    'c': '🔧',
    'javascript': '🟨',
    'typescript': '🔷',
    'python': '🐍',
    'java': '☕',
    'swift': '🦉',
    'kotlin': '🎯',
    'lua': '🌙'
  };
  return icons[language.toLowerCase()] || '💻';
}

/**
 * 获取平台图标
 */
function getPlatformIcon(platform?: string): string {
  if (!platform) return '🌐';
  
  const icons: { [key: string]: string } = {
    'android': '🤖',
    'ios': '🍎',
    'windows': '🪟',
    'macos': '💻',
    'linux': '🐧',
    'web': '🌐',
    'universal': '🌍',
    '通用': '🌍'
  };
  return icons[platform.toLowerCase()] || '🌐';
}

/**
 * 生成代码质量进度条
 */
function generateQualityBar(score: number): string {
  const normalizedScore = Math.max(0, Math.min(10, score));
  const stars = Math.round(normalizedScore / 2);
  return '⭐'.repeat(stars) + '☆'.repeat(5 - stars);
}

/**
 * 增强的API参考响应格式化
 */
export function formatApiReferenceResponse(
  data: ApiReference, 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 2500,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data) {
    return `${t.noResults}。请检查类名是否正确。`;
  }

  let response = `${t.apiReference}\n\n`;
  
  // 类信息头部
  const classIcon = opts.visualEnhancement ? '🏗️' : '';
  response += `### ${classIcon} ${data.className}\n`;
  response += `${t.namespace}: \`${data.namespace}\`\n`;
  
  // 智能截断描述
  const maxDescLength = opts.compact ? 150 : 300;
  const description = smartTruncate(data.description, maxDescLength);
  response += `${t.description}: ${description}\n`;
  
  response += `${t.sourceFile}: \`${data.sourceFile}\`\n`;
  response += `${t.docLink}: ${data.documentationUrl}\n`;
  
  // 继承信息（如果有）
  if ((data as any).inheritance) {
    response += `**继承关系**: \`${(data as any).inheritance}\`\n`;
  }
  
  response += '\n';

  // 方法信息
  if (data.methodName) {
    const methodIcon = opts.visualEnhancement ? '⚙️' : '';
    response += `#### ${methodIcon} ${t.method}: ${data.methodName}\n`;
    
    if (data.returnType) {
      response += `${t.returnType}: \`${data.returnType}\`\n`;
    }
    
    if (data.parameters && data.parameters.length > 0) {
      response += `${t.parameters}:\n`;
      data.parameters.forEach((param, index) => {
        const paramIcon = opts.visualEnhancement ? (index % 2 === 0 ? '📥' : '📤') : '•';
        response += `  ${paramIcon} \`${param.type} ${param.name}\`${param.optional ? ` ${t.optional}` : ''}: ${param.description}\n`;
        if (param.defaultValue) {
          response += `    ${t.defaultValue}: \`${param.defaultValue}\`\n`;
        }
      });
      response += '\n';
    }
  }

  // 使用示例
  if (data.examples && data.examples.length > 0) {
    const exampleIcon = opts.visualEnhancement ? '💡' : '';
    response += `#### ${exampleIcon} ${t.usageExamples}\n`;
    
    const maxExamples = opts.compact ? 2 : data.examples.length;
    data.examples.slice(0, maxExamples).forEach((example, index) => {
      response += `**示例 ${index + 1}**: ${example.title}\n`;
      
      // 代码长度限制
      const maxCodeLength = opts.compact ? 300 : 600;
      const code = example.code.length > maxCodeLength ? 
        smartTruncate(example.code, maxCodeLength, '\n// ... 代码已截断') : 
        example.code;
      
      response += `\`\`\`${example.language}\n${code}\n\`\`\`\n`;
      
      const exampleDesc = smartTruncate(example.description, 150);
      response += `${exampleDesc}\n\n`;
    });
    
    if (data.examples.length > maxExamples) {
      response += `_... 还有 ${data.examples.length - maxExamples} 个示例_\n\n`;
    }
  }

  // 相关API
  if (data.relatedApis && data.relatedApis.length > 0) {
    const relatedIcon = opts.visualEnhancement ? '🔗' : '';
    response += `#### ${relatedIcon} ${t.relatedApis}\n`;
    
    const maxRelated = opts.compact ? 5 : data.relatedApis.length;
    data.relatedApis.slice(0, maxRelated).forEach(api => {
      response += `- \`${api}\`\n`;
    });
    
    if (data.relatedApis.length > maxRelated) {
      response += `- _... 还有 ${data.relatedApis.length - maxRelated} 个相关API_\n`;
    }
    response += '\n';
  }
  
  // 元数据信息
  if (opts.includeMetadata && metadata) {
    response += `---\n💾 **查询信息**: 耗时 ${metadata.searchTime || 0}ms`;
    if (metadata.cacheHit) {
      response += ` | 📦 缓存命中`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 增强的构建问题响应格式化
 */
export function formatBuildIssueResponse(
  data: BuildIssue, 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 3500,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data) {
    return `${t.noResults}。请提供更详细的错误信息。`;
  }

  let response = `${t.buildDiagnostic}\n\n`;
  
  // 问题概述
  const platformIcon = getPlatformIcon(data.platform);
  response += `${t.platform}: ${data.platform} ${platformIcon}\n`;
  response += `${t.errorType}: ${getBuildErrorTypeLabel(data.errorType)}\n`;
  
  // 智能截断错误信息
  const maxErrorLength = opts.compact ? 200 : 400;
  const errorMessage = smartTruncate(data.errorMessage, maxErrorLength);
  response += `${t.errorMessage}: \`${errorMessage}\`\n\n`;

  // 解决方案
  if (data.solutions && data.solutions.length > 0) {
    // 按优先级和验证状态排序
    const sortedSolutions = data.solutions.sort((a, b) => {
      if (a.verified !== b.verified) return a.verified ? -1 : 1;
      const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
    
    const maxSolutions = opts.compact ? 3 : sortedSolutions.length;
    const displaySolutions = sortedSolutions.slice(0, maxSolutions);
    
    const solutionIcon = opts.visualEnhancement ? '🛠️' : '';
    response += `### ${solutionIcon} ${t.solutions} (${data.solutions.length} 个)\n\n`;
    
    displaySolutions.forEach((solution, index) => {
      const priorityIcon = getPriorityIcon(solution.priority);
      const verifiedIcon = solution.verified ? '✅' : '⚠️';
      
      response += `#### ${priorityIcon} ${index + 1}. ${solution.title}\n`;
      response += `${t.priority}: ${getPriorityLabel(solution.priority)}\n`;
      response += `${t.verified}: ${verifiedIcon} ${solution.verified ? '已验证' : '未验证'}\n`;
      
      const maxDescLength = opts.compact ? 100 : 200;
      const description = smartTruncate(solution.description, maxDescLength);
      response += `${t.description}: ${description}\n\n`;
      
      // 解决步骤
      if (solution.steps && solution.steps.length > 0) {
        const stepIcon = opts.visualEnhancement ? '📝' : '';
        response += `${stepIcon} ${t.steps}:\n`;
        const maxSteps = opts.compact ? 5 : solution.steps.length;
        solution.steps.slice(0, maxSteps).forEach((step, stepIndex) => {
          response += `  ${stepIndex + 1}. ${step}\n`;
        });
        
        if (solution.steps.length > maxSteps) {
          response += `  ... 还有 ${solution.steps.length - maxSteps} 个步骤\n`;
        }
        response += '\n';
      }
      
      // 代码修改
      if (solution.codeChanges && solution.codeChanges.length > 0) {
        const codeIcon = opts.visualEnhancement ? '💻' : '';
        response += `${codeIcon} ${t.codeChanges}:\n`;
        
        const maxChanges = opts.compact ? 2 : solution.codeChanges.length;
        solution.codeChanges.slice(0, maxChanges).forEach(change => {
          response += `  • **文件**: \`${change.file}\`\n`;
          response += `    **说明**: ${change.explanation}\n`;
          if (change.oldCode) {
            response += `    **原代码**: \`${change.oldCode}\`\n`;
          }
          const newCode = opts.compact ? 
            smartTruncate(change.newCode, 100) : change.newCode;
          response += `    **新代码**: \`${newCode}\`\n`;
        });
        
        if (solution.codeChanges.length > maxChanges) {
          response += `  ... 还有 ${solution.codeChanges.length - maxChanges} 个代码修改\n`;
        }
        response += '\n';
      }
      
      // 配置修改
      if (solution.configChanges && solution.configChanges.length > 0) {
        const configIcon = opts.visualEnhancement ? '⚙️' : '';
        response += `${configIcon} ${t.configChanges}:\n`;
        
        const maxConfigs = opts.compact ? 3 : solution.configChanges.length;
        solution.configChanges.slice(0, maxConfigs).forEach(change => {
          response += `  • **文件**: \`${change.file}\`\n`;
          response += `    **设置**: \`${change.setting} = ${change.value}\`\n`;
          response += `    **说明**: ${change.explanation}\n`;
        });
        
        if (solution.configChanges.length > maxConfigs) {
          response += `  ... 还有 ${solution.configChanges.length - maxConfigs} 个配置修改\n`;
        }
        response += '\n';
      }
    });
    
    if (data.solutions.length > maxSolutions) {
      response += `💡 显示了前 ${maxSolutions} 个最佳解决方案，共 ${data.solutions.length} 个方案可用。\n\n`;
    }
  }

  // 相关问题
  if (data.relatedIssues && data.relatedIssues.length > 0) {
    const issueIcon = opts.visualEnhancement ? '🔗' : '';
    response += `### ${issueIcon} ${t.relatedIssues}\n`;
    
    const maxIssues = opts.compact ? 3 : data.relatedIssues.length;
    data.relatedIssues.slice(0, maxIssues).forEach(issue => {
      response += `- ${issue}\n`;
    });
    
    if (data.relatedIssues.length > maxIssues) {
      response += `- _... 还有 ${data.relatedIssues.length - maxIssues} 个相关问题_\n`;
    }
    response += '\n';
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n🔍 **诊断信息**: 耗时 ${metadata.searchTime || 0}ms | 检查了 ${metadata.resultsCount || 0} 个解决方案`;
    if (metadata.sources) {
      response += ` | 数据源: ${metadata.sources.join(', ')}`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 获取优先级图标
 */
function getPriorityIcon(priority: string): string {
  const icons: { [key: string]: string } = {
    'high': '🔴',
    'medium': '🟡',
    'low': '🟢'
  };
  return icons[priority] || '⚪';
}

/**
 * 增强的迁移指南响应格式化
 */
export function formatMigrationGuideResponse(
  data: MigrationGuide, 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 3000,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data) {
    return `${t.noResults}。请检查源引擎版本是否正确。`;
  }

  let response = `${t.migrationGuide}: ${data.fromEngine} → ${data.toEngine}\n\n`;
  
  response += `**主题**: ${data.topic}\n\n`;

  // API变更
  if (data.changes && data.changes.length > 0) {
    // 按破坏性变更优先排序
    const sortedChanges = data.changes.sort((a, b) => {
      if (a.breaking !== b.breaking) return a.breaking ? -1 : 1;
      return 0;
    });
    
    const maxChanges = opts.compact ? 5 : sortedChanges.length;
    const displayChanges = sortedChanges.slice(0, maxChanges);
    
    const changeIcon = opts.visualEnhancement ? '🔄' : '';
    response += `### ${changeIcon} ${t.apiChanges} (${data.changes.length} 个)\n\n`;
    
    displayChanges.forEach((change, index) => {
      const breakingIcon = change.breaking ? '⚠️' : '✅';
      
      response += `#### ${breakingIcon} ${index + 1}. ${change.category}\n`;
      response += `**旧API**: \`${change.oldApi}\`\n`;
      response += `**新API**: \`${change.newApi}\`\n`;
      
      const maxDescLength = opts.compact ? 150 : 250;
      const description = smartTruncate(change.description, maxDescLength);
      response += `**描述**: ${description}\n`;
      response += `**破坏性变更**: ${change.breaking ? '⚠️ 是' : '✅ 否'}\n`;
      
      // 迁移步骤
      if (change.migrationSteps && change.migrationSteps.length > 0) {
        const stepIcon = opts.visualEnhancement ? '📝' : '';
        response += `${stepIcon} **迁移步骤**:\n`;
        
        const maxSteps = opts.compact ? 3 : change.migrationSteps.length;
        change.migrationSteps.slice(0, maxSteps).forEach((step, stepIndex) => {
          response += `  ${stepIndex + 1}. ${step}\n`;
        });
        
        if (change.migrationSteps.length > maxSteps) {
          response += `  ... 还有 ${change.migrationSteps.length - maxSteps} 个步骤\n`;
        }
      }
      response += '\n';
    });
    
    if (data.changes.length > maxChanges) {
      response += `💡 显示了前 ${maxChanges} 个最重要的变更，共 ${data.changes.length} 个变更。\n\n`;
    }
  }

  // 迁移示例
  if (data.examples && data.examples.length > 0) {
    const maxExamples = opts.compact ? 2 : data.examples.length;
    const displayExamples = data.examples.slice(0, maxExamples);
    
    const exampleIcon = opts.visualEnhancement ? '💻' : '';
    response += `### ${exampleIcon} 迁移示例\n\n`;
    
    displayExamples.forEach((example, index) => {
      response += `#### ${example.title}\n`;
      
      const maxCodeLength = opts.compact ? 400 : 600;
      const code = example.code.length > maxCodeLength ? 
        smartTruncate(example.code, maxCodeLength, '\n// ... 代码已截断') : 
        example.code;
      
      response += `\`\`\`${example.language}\n${code}\n\`\`\`\n`;
      
      const exampleDesc = smartTruncate(example.description, 150);
      response += `${exampleDesc}\n\n`;
    });
    
    if (data.examples.length > maxExamples) {
      response += `_... 还有 ${data.examples.length - maxExamples} 个迁移示例_\n\n`;
    }
  }

  // 常见问题
  if (data.commonIssues && data.commonIssues.length > 0) {
    const maxIssues = opts.compact ? 3 : data.commonIssues.length;
    const displayIssues = data.commonIssues.slice(0, maxIssues);
    
    const issueIcon = opts.visualEnhancement ? '⚠️' : '';
    response += `### ${issueIcon} ${t.commonIssues}\n\n`;
    
    displayIssues.forEach((issue, index) => {
      const platformIcon = getPlatformIcon(issue.platform);
      
      response += `#### ${index + 1}. ${issue.errorMessage}\n`;
      response += `**平台**: ${issue.platform} ${platformIcon} | **类型**: ${getBuildErrorTypeLabel(issue.errorType)}\n`;
      
      if (issue.solutions && issue.solutions.length > 0) {
        response += `**解决方案**: ${issue.solutions[0].title}\n`;
      }
      response += '\n';
    });
    
    if (data.commonIssues.length > maxIssues) {
      response += `⚠️ 还有 ${data.commonIssues.length - maxIssues} 个常见问题。\n\n`;
    }
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n🔄 **迁移指南**: 耗时 ${metadata.searchTime || 0}ms`;
    if (metadata.cacheHit) {
      response += ` | 📦 缓存命中`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 增强的平台信息响应格式化
 */
export function formatPlatformInfoResponse(
  data: PlatformInfo, 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 2500,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data) {
    return `${t.noResults}。请检查平台名称是否正确。`;
  }

  const platformIcon = getPlatformIcon(data.platform);
  let response = `${t.platformInfo}: ${data.platform} ${platformIcon}\n\n`;
  
  response += `**主题**: ${data.topic}\n\n`;

  // 系统要求
  if (data.requirements && data.requirements.length > 0) {
    const reqIcon = opts.visualEnhancement ? '📝' : '';
    response += `### ${reqIcon} ${t.systemRequirements}\n`;
    
    const maxReqs = opts.compact ? 5 : data.requirements.length;
    data.requirements.slice(0, maxReqs).forEach(req => {
      response += `  • ${req}\n`;
    });
    
    if (data.requirements.length > maxReqs) {
      response += `  ... 还有 ${data.requirements.length - maxReqs} 个要求\n`;
    }
    response += '\n';
  }

  // 配置设置
  if (data.configuration && data.configuration.length > 0) {
    const configIcon = opts.visualEnhancement ? '⚙️' : '';
    response += `### ${configIcon} ${t.configSettings}\n`;
    
    const maxConfigs = opts.compact ? 3 : data.configuration.length;
    data.configuration.slice(0, maxConfigs).forEach(config => {
      response += `#### 📄 ${config.file}\n`;
      response += `  • **设置**: \`${config.setting}\`\n`;
      response += `  • **值**: \`${config.value}\`\n`;
      
      const maxExplanation = opts.compact ? 100 : 200;
      const explanation = smartTruncate(config.explanation, maxExplanation);
      response += `  • **说明**: ${explanation}\n\n`;
    });
    
    if (data.configuration.length > maxConfigs) {
      response += `⚙️ 还有 ${data.configuration.length - maxConfigs} 个配置设置。\n\n`;
    }
  }

  // 构建步骤
  if (data.buildSteps && data.buildSteps.length > 0) {
    const buildIcon = opts.visualEnhancement ? '🔨' : '';
    response += `### ${buildIcon} ${t.buildSteps}\n`;
    
    const maxSteps = opts.compact ? 5 : data.buildSteps.length;
    data.buildSteps.slice(0, maxSteps).forEach((step, index) => {
      response += `  ${index + 1}. ${step}\n`;
    });
    
    if (data.buildSteps.length > maxSteps) {
      response += `  ... 还有 ${data.buildSteps.length - maxSteps} 个步骤\n`;
    }
    response += '\n';
  }

  // 配置示例
  if (data.examples && data.examples.length > 0) {
    const exampleIcon = opts.visualEnhancement ? '💻' : '';
    response += `### ${exampleIcon} ${t.configExamples}\n`;
    
    const maxExamples = opts.compact ? 2 : data.examples.length;
    data.examples.slice(0, maxExamples).forEach((example, index) => {
      response += `#### ${example.title}\n`;
      
      const maxCodeLength = opts.compact ? 300 : 500;
      const code = example.code.length > maxCodeLength ? 
        smartTruncate(example.code, maxCodeLength, '\n// ... 代码已截断') : 
        example.code;
      
      response += `\`\`\`${example.language}\n${code}\n\`\`\`\n`;
      
      const exampleDesc = smartTruncate(example.description, 150);
      response += `${exampleDesc}\n\n`;
    });
    
    if (data.examples.length > maxExamples) {
      response += `_... 还有 ${data.examples.length - maxExamples} 个配置示例_\n\n`;
    }
  }

  // 常见问题
  if (data.commonIssues && data.commonIssues.length > 0) {
    const issueIcon = opts.visualEnhancement ? '⚠️' : '';
    response += `### ${issueIcon} ${t.commonIssues}\n`;
    
    const maxIssues = opts.compact ? 3 : data.commonIssues.length;
    data.commonIssues.slice(0, maxIssues).forEach((issue, index) => {
      response += `#### ${index + 1}. ${issue.errorMessage}\n`;
      
      if (issue.solutions && issue.solutions.length > 0) {
        response += `**解决方案**: ${issue.solutions[0].title}\n`;
      }
      response += '\n';
    });
    
    if (data.commonIssues.length > maxIssues) {
      response += `⚠️ 还有 ${data.commonIssues.length - maxIssues} 个常见问题。\n\n`;
    }
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n🎯 **平台信息**: 耗时 ${metadata.searchTime || 0}ms`;
    if (metadata.sources) {
      response += ` | 数据源: ${metadata.sources.join(', ')}`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 获取资源类型标签
 */
function getResourceTypeLabel(type: string): string {
  const labels: { [key: string]: string } = {
    'official_docs': '官方文档',
    'wiki': 'Wiki',
    'source': '源码',
    'example': '示例',
    'header': '头文件',
    'document': '文档',
    'web': '网络资源',
    'community': '社区'
  };
  return labels[type] || type;
}

/**
 * 获取构建错误类型标签
 */
function getBuildErrorTypeLabel(type: string): string {
  const labels: { [key: string]: string } = {
    'compile': '编译错误',
    'link': '链接错误',
    'runtime': '运行时错误',
    'configuration': '配置错误'
  };
  return labels[type] || type;
}

/**
 * 获取优先级标签
 */
function getPriorityLabel(priority: string): string {
  const labels: { [key: string]: string } = {
    'high': '🔴 高',
    'medium': '🟡 中',
    'low': '🟢 低'
  };
  return labels[priority] || priority;
}

/**
 * 增强的代码分析响应格式化
 */
export function formatCodeAnalysisResponse(
  data: CodeAnalysisResult, 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 4000,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data) {
    return `${t.noResults}。请检查代码格式是否正确。`;
  }

  let response = `${t.codeAnalysis}\n\n`;

  // 代码基本信息
  const langIcon = getLanguageIcon(data.language);
  response += `${t.language}: ${data.language.toUpperCase()} ${langIcon}\n`;
  response += `**代码长度**: ${data.code.length} 字符\n`;
  
  // 代码质量概览
  const totalIssues = (data.issues?.length || 0);
  const criticalIssues = data.issues?.filter(i => (i as any).severity === 'critical').length || 0;
  const qualityScore = Math.max(0, 10 - totalIssues * 0.5 - criticalIssues * 2);
  const qualityBar = generateQualityBar(qualityScore);
  
  response += `**代码质量**: ${qualityScore.toFixed(1)}/10 ${qualityBar}\n\n`;

  // 发现的问题
  if (data.issues && data.issues.length > 0) {
    // 按严重性排序
    const sortedIssues = data.issues.sort((a: any, b: any) => {
      const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 } as any;
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
    
    const maxIssues = opts.compact ? 5 : sortedIssues.length;
    const displayIssues = sortedIssues.slice(0, maxIssues);
    
    const issueIcon = opts.visualEnhancement ? '⚠️' : '';
    response += `### ${issueIcon} ${t.foundIssues} (${data.issues.length} 个)\n\n`;
    
    displayIssues.forEach((issue, index) => {
      const typeIcon = issue.type === 'error' ? '❌' : 
                       issue.type === 'warning' ? '⚠️' : 'ℹ️';
      const severityIcon = getSeverityIcon(issue.severity);
      
      response += `#### ${typeIcon} ${index + 1}. ${issue.message}\n`;
      response += `**行号**: L${issue.line} | **严重性**: ${getSeverityLabel(issue.severity)} ${severityIcon}\n`;
      response += `**规则**: \`${issue.rule}\`\n\n`;
    });
    
    if (data.issues.length > maxIssues) {
      response += `💡 显示了前 ${maxIssues} 个最严重的问题，共 ${data.issues.length} 个问题。\n\n`;
    }
  }

  // 改进建议
  if (data.suggestions && data.suggestions.length > 0) {
    const maxSuggestions = opts.compact ? 3 : data.suggestions.length;
    const displaySuggestions = data.suggestions.slice(0, maxSuggestions);
    
    const suggestionIcon = opts.visualEnhancement ? '💡' : '';
    response += `### ${suggestionIcon} ${t.suggestions} (${data.suggestions.length} 个)\n\n`;
    
    displaySuggestions.forEach((suggestion, index) => {
      response += `#### ${index + 1}. ${suggestion.message}\n`;
      response += `**行号**: L${suggestion.line} | **类别**: ${suggestion.category}\n`;
      response += `**原因**: ${suggestion.reason}\n`;
      
      const maxCodeLength = opts.compact ? 80 : 150;
      const suggestedCode = smartTruncate(suggestion.suggestedCode, maxCodeLength);
      response += `**建议代码**: \`${suggestedCode}\`\n\n`;
    });
    
    if (data.suggestions.length > maxSuggestions) {
      response += `💡 还有 ${data.suggestions.length - maxSuggestions} 个改进建议。\n\n`;
    }
  }

  // 相关最佳实践
  if (data.bestPractices && data.bestPractices.length > 0) {
    const maxPractices = opts.compact ? 2 : data.bestPractices.length;
    const displayPractices = data.bestPractices.slice(0, maxPractices);
    
    const practiceIcon = opts.visualEnhancement ? '✨' : '';
    response += `### ${practiceIcon} ${t.bestPracticesSection} (${data.bestPractices.length} 个)\n\n`;
    
    displayPractices.forEach((practice, index) => {
      response += `#### ${index + 1}. ${practice.title}\n`;
      response += `**类别**: ${practice.category}\n`;
      
      const maxDescLength = opts.compact ? 100 : 200;
      const description = smartTruncate(practice.description, maxDescLength);
      response += `${t.description}: ${description}\n\n`;
    });
    
    if (data.bestPractices.length > maxPractices) {
      response += `💡 还有 ${data.bestPractices.length - maxPractices} 个相关最佳实践。\n\n`;
    }
  }

  // 性能优化建议
  if (data.performance && data.performance.length > 0) {
    const maxPerf = opts.compact ? 3 : data.performance.length;
    const displayPerf = data.performance.slice(0, maxPerf);
    
    const perfIcon = opts.visualEnhancement ? '🚀' : '';
    response += `### ${perfIcon} ${t.performanceOptimization} (${data.performance.length} 个)\n\n`;
    
    displayPerf.forEach((perf, index) => {
      const impactIcon = getImpactIcon(perf.impact);
      response += `#### ${impactIcon} ${index + 1}. ${perf.aspect}\n`;
      response += `**建议**: ${perf.recommendation}\n`;
      response += `**影响**: ${getImpactLabel(perf.impact)}\n`;
      if (perf.measurement) {
        response += `**预期效果**: ${perf.measurement}\n`;
      }
      response += '\n';
    });
    
    if (data.performance.length > maxPerf) {
      response += `💡 还有 ${data.performance.length - maxPerf} 个性能优化建议。\n\n`;
    }
  }

  // 安全检查
  if (data.security && data.security.length > 0) {
    // 按严重性排序
    const sortedSecurity = data.security.sort((a: any, b: any) => {
      const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 } as any;
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
    
    const maxSecurity = opts.compact ? 3 : sortedSecurity.length;
    const displaySecurity = sortedSecurity.slice(0, maxSecurity);
    
    const securityIcon = opts.visualEnhancement ? '🔒' : '';
    response += `### ${securityIcon} ${t.securityCheck} (${data.security.length} 个)\n\n`;
    
    displaySecurity.forEach((security, index) => {
      const severityIcon = security.severity === 'critical' ? '🚨' : 
                           security.severity === 'high' ? '🔴' : '⚠️';
      
      response += `#### ${severityIcon} ${index + 1}. ${security.description}\n`;
      response += `**类型**: ${security.type === 'vulnerability' ? '安全漏洞' : '最佳实践'}\n`;
      response += `**严重性**: ${getSeverityLabel(security.severity)}\n`;
      
      const maxRecLength = opts.compact ? 100 : 200;
      const recommendation = smartTruncate(security.recommendation, maxRecLength);
      response += `**建议**: ${recommendation}\n\n`;
    });
    
    if (data.security.length > maxSecurity) {
      response += `⚠️ 还有 ${data.security.length - maxSecurity} 个安全检查项。\n\n`;
    }
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n📊 **分析统计**: 耗时 ${metadata.searchTime || 0}ms | 检查了 ${data.code.length} 字符代码`;
    if (metadata.cacheHit) {
      response += ` | 📦 缓存命中`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 获取严重性图标
 */
function getSeverityIcon(severity: string): string {
  const icons: { [key: string]: string } = {
    'critical': '🚨',
    'high': '🔴',
    'medium': '🟡',
    'low': '🟢'
  };
  return icons[severity] || '⚪';
}

/**
 * 获取影响图标
 */
function getImpactIcon(impact: string): string {
  const icons: { [key: string]: string } = {
    'breaking': '💥',
    'high': '🔴',
    'medium': '🟡',
    'low': '🟢',
    'compatible': '✅',
    'enhancement': '✨'
  };
  return icons[impact] || '🟦';
}

/**
 * 增强的版本对比响应格式化
 */
export function formatVersionComparisonResponse(
  data: VersionComparison, 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 2500,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data) {
    return `${t.noResults}。请检查版本号是否正确。`;
  }

  let response = `${t.versionComparison}\n\n`;

  response += `**功能**: ${data.feature}\n`;
  response += `**对比版本**: ${data.versions.join(' ↔️ ')}\n\n`;

  // 版本变更
  if (data.changes && data.changes.length > 0) {
    // 按影响级别和变更类型排序
    const sortedChanges = data.changes.sort((a, b) => {
      const impactOrder = { 'breaking': 4, 'high': 3, 'medium': 2, 'low': 1 };
      const impactA = impactOrder[a.impact as keyof typeof impactOrder] || 1;
      const impactB = impactOrder[b.impact as keyof typeof impactOrder] || 1;
      return impactB - impactA;
    });
    
    const maxChanges = opts.compact ? 5 : sortedChanges.length;
    const displayChanges = sortedChanges.slice(0, maxChanges);
    
    const changeIcon = opts.visualEnhancement ? '🔄' : '';
    response += `### ${changeIcon} ${t.versionChanges} (${data.changes.length} 个)\n\n`;
    
    displayChanges.forEach((change, index) => {
      const typeIcon = change.changeType === 'added' ? '✅' :
                       change.changeType === 'removed' ? '❌' :
                       change.changeType === 'deprecated' ? '⚠️' : '🔄';
      const impactIcon = getImpactIcon(change.impact);
      
      response += `#### ${typeIcon} ${index + 1}. ${change.description}\n`;
      response += `**版本**: ${change.version} | **类型**: ${getChangeTypeLabel(change.changeType)}\n`;
      response += `**影响**: ${getImpactLabel(change.impact)} ${impactIcon}\n\n`;
    });
    
    if (data.changes.length > maxChanges) {
      response += `💡 显示了前 ${maxChanges} 个最重要的变更，共 ${data.changes.length} 个变更。\n\n`;
    }
  }

  // 兼容性信息
  if (data.compatibility && data.compatibility.length > 0) {
    const compatIcon = opts.visualEnhancement ? '🔗' : '';
    response += `### ${compatIcon} ${t.compatibilityInfo}\n\n`;
    
    const maxCompat = opts.compact ? 5 : data.compatibility.length;
    data.compatibility.slice(0, maxCompat).forEach(compat => {
      const icon = compat.compatible ? '✅' : '❌';
      response += `  ${icon} **${compat.version}**: ${compat.notes}\n`;
    });
    
    if (data.compatibility.length > maxCompat) {
      response += `  ... 还有 ${data.compatibility.length - maxCompat} 个兼容性信息\n`;
    }
    response += '\n';
  }

  // 迁移说明
  if (data.migrationNotes && data.migrationNotes.length > 0) {
    const migrationIcon = opts.visualEnhancement ? '📝' : '';
    response += `### ${migrationIcon} ${t.migrationNotes}\n\n`;
    
    const maxNotes = opts.compact ? 3 : data.migrationNotes.length;
    data.migrationNotes.slice(0, maxNotes).forEach(note => {
      response += `  • ${note}\n`;
    });
    
    if (data.migrationNotes.length > maxNotes) {
      response += `  ... 还有 ${data.migrationNotes.length - maxNotes} 个迁移说明\n`;
    }
    response += '\n';
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n🔄 **版本对比**: 耗时 ${metadata.searchTime || 0}ms`;
    if (metadata.cacheHit) {
      response += ` | 📦 缓存命中`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 增强的最佳实践响应格式化
 */
export function formatBestPracticesResponse(
  data: BestPractice[], 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 3500,
    includeMetadata: true,
    sortByRelevance: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data || data.length === 0) {
    return `${t.noResults}。请尝试使用不同的用例关键词。`;
  }

  // 按相关性和类别排序
  const sortedData = opts.sortByRelevance ? 
    sortByRelevance(data, metadata?.query).sort((a, b) => {
      // 二次排序：按类别重要性
      const categoryPriority = {
        'performance': 3,
        'memory': 3,
        'architecture': 2,
        'optimization': 2,
        'general': 1
      };
      const priorityA = categoryPriority[a.category as keyof typeof categoryPriority] || 1;
      const priorityB = categoryPriority[b.category as keyof typeof categoryPriority] || 1;
      return priorityB - priorityA;
    }) : data;
  
  const displayLimit = opts.compact ? 5 : sortedData.length;
  const displayData = sortedData.slice(0, displayLimit);
  
  let response = `${t.bestPractices} (${data.length} 个建议)`;
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `\n🔍 **搜索耗时**: ${metadata.searchTime || 0}ms`;
    if (metadata.sources) {
      response += ` | **数据源**: ${metadata.sources.join(', ')}`;
    }
  }
  
  response += '\n\n';

  displayData.forEach((practice, index) => {
    const categoryIcon = getCategoryIcon(practice.category);
    
    if (opts.visualEnhancement) {
      response += `### ${categoryIcon} ${index + 1}. ${practice.title}\n`;
    } else {
      response += `### ${index + 1}. ${practice.title}\n`;
    }
    
    response += `**类别**: ${practice.category} ${categoryIcon}\n`;
    response += `**用例**: ${practice.useCase}\n`;
    
    // 智能截断描述
    const maxDescLength = opts.compact ? 150 : 250;
    const description = smartTruncate(practice.description, maxDescLength);
    response += `${t.description}: ${description}\n\n`;

    // 建议
    if (practice.recommendations && practice.recommendations.length > 0) {
      const recIcon = opts.visualEnhancement ? '✅' : '';
      response += `#### ${recIcon} ${t.recommendations}\n`;
      
      const maxRecs = opts.compact ? 3 : practice.recommendations.length;
      practice.recommendations.slice(0, maxRecs).forEach(rec => {
        response += `  • ${rec}\n`;
      });
      
      if (practice.recommendations.length > maxRecs) {
        response += `  ... 还有 ${practice.recommendations.length - maxRecs} 个建议\n`;
      }
      response += '\n';
    }

    // 代码示例
    if (practice.examples && practice.examples.length > 0) {
      const exampleIcon = opts.visualEnhancement ? '💻' : '';
      response += `#### ${exampleIcon} 代码示例\n`;
      
      const maxExamples = opts.compact ? 1 : 2;
      practice.examples.slice(0, maxExamples).forEach(example => {
        response += `**${example.title}**:\n`;
        
        // 代码长度限制
        const maxCodeLength = opts.compact ? 300 : 500;
        const code = example.code.length > maxCodeLength ? 
          smartTruncate(example.code, maxCodeLength, '\n// ... 代码已截断') : 
          example.code;
        
        response += `\`\`\`${example.language}\n${code}\n\`\`\`\n`;
        
        const exampleDesc = smartTruncate(example.description, 100);
        response += `${exampleDesc}\n\n`;
      });
      
      if (practice.examples.length > maxExamples) {
        response += `_... 还有 ${practice.examples.length - maxExamples} 个代码示例_\n\n`;
      }
    }

    // 应避免的做法
    if (practice.antiPatterns && practice.antiPatterns.length > 0) {
      const antiIcon = opts.visualEnhancement ? '❌' : '';
      response += `#### ${antiIcon} ${t.antiPatterns}\n`;
      
      const maxAnti = opts.compact ? 3 : practice.antiPatterns.length;
      practice.antiPatterns.slice(0, maxAnti).forEach(pattern => {
        response += `  ❌ ${pattern}\n`;
      });
      
      if (practice.antiPatterns.length > maxAnti) {
        response += `  ... 还有 ${practice.antiPatterns.length - maxAnti} 个反模式\n`;
      }
      response += '\n';
    }

    // 性能影响
    if (practice.performance && practice.performance.length > 0) {
      const perfIcon = opts.visualEnhancement ? '🚀' : '';
      response += `#### ${perfIcon} ${t.performanceImpact}\n`;
      
      const maxPerf = opts.compact ? 2 : practice.performance.length;
      practice.performance.slice(0, maxPerf).forEach(perf => {
        const impactIcon = getImpactIcon(perf.impact);
        response += `  ${impactIcon} **${perf.aspect}**: ${perf.recommendation}\n`;
        response += `    影响: ${getImpactLabel(perf.impact)}`;
        if (perf.measurement) {
          response += ` | 预期效果: ${perf.measurement}`;
        }
        response += '\n';
      });
      
      if (practice.performance.length > maxPerf) {
        response += `  ... 还有 ${practice.performance.length - maxPerf} 个性能建议\n`;
      }
      response += '\n';
    }
  });
  
  // 截断提示
  if (data.length > displayLimit) {
    response += `💡 显示了前 ${displayLimit} 个最相关的实践，共 ${data.length} 个实践可用。\n`;
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 获取类别图标
 */
function getCategoryIcon(category: string): string {
  const icons: { [key: string]: string } = {
    'performance': '🚀',
    'memory': '💾',
    'architecture': '🏗️',
    'optimization': '⚙️',
    'security': '🔒',
    'general': '💡',
    'testing': '🧪',
    'debugging': '🐛'
  };
  return icons[category.toLowerCase()] || '💡';
}

/**
 * 增强的社区解决方案响应格式化
 */
export function formatCommunityResponse(
  data: CommunitySolution, 
  metadata: any, 
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 3000,
    includeMetadata: true,
    sortByRelevance: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  const t = i18n[opts.language] as any;
  
  if (!data) {
    return `${t.noResults}。请尝试使用不同的问题描述。`;
  }

  let response = `${t.communitySolution}\n\n`;

  // 问题描述
  const maxProblemLength = opts.compact ? 200 : 300;
  const problem = smartTruncate(data.problem, maxProblemLength);
  response += `**问题**: ${problem}\n\n`;

  // 解决方案
  if (data.solutions && data.solutions.length > 0) {
    // 按投票数和验证状态排序
    const sortedSolutions = data.solutions.sort((a, b) => {
      if (a.verified !== b.verified) return a.verified ? -1 : 1;
      return b.votes - a.votes;
    });
    
    const maxSolutions = opts.compact ? 3 : sortedSolutions.length;
    const displaySolutions = sortedSolutions.slice(0, maxSolutions);
    
    const solutionIcon = opts.visualEnhancement ? '💡' : '';
    response += `### ${solutionIcon} 解决方案 (${data.solutions.length} 个)\n\n`;
    
    displaySolutions.forEach((solution, index) => {
      const verifiedIcon = solution.verified ? '✅' : '⚠️';
      const voteIcon = getVoteIcon(solution.votes);
      
      response += `#### ${voteIcon} ${index + 1}. ${solution.title}\n`;
      response += `**作者**: ${solution.author} | ${t.verified}: ${verifiedIcon} ${solution.verified ? '已验证' : '未验证'}\n`;
      response += `**投票数**: ${solution.votes} ${voteIcon} | **来源**: ${solution.sourceUrl}\n`;
      
      // 智能截断描述
      const maxDescLength = opts.compact ? 150 : 250;
      const description = smartTruncate(solution.description, maxDescLength);
      response += `${t.description}: ${description}\n`;

      if (solution.tags && solution.tags.length > 0) {
        const maxTags = opts.compact ? 3 : solution.tags.length;
        const displayTags = solution.tags.slice(0, maxTags).join(', ');
        response += `**标签**: ${displayTags}`;
        if (solution.tags.length > maxTags) {
          response += ` (+${solution.tags.length - maxTags})`;
        }
        response += '\n';
      }
      response += '\n';
    });
    
    if (data.solutions.length > maxSolutions) {
      response += `💡 显示了前 ${maxSolutions} 个最佳解决方案，共 ${data.solutions.length} 个方案可用。\n\n`;
    }
  }

  // 相关讨论
  if (data.discussions && data.discussions.length > 0) {
    // 按最后活动时间排序
    const sortedDiscussions = data.discussions.sort((a, b) => 
      new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
    );
    
    const maxDiscussions = opts.compact ? 3 : sortedDiscussions.length;
    const displayDiscussions = sortedDiscussions.slice(0, maxDiscussions);
    
    const discussionIcon = opts.visualEnhancement ? '💬' : '';
    response += `### ${discussionIcon} ${t.relatedDiscussions} (${data.discussions.length} 个)\n\n`;
    
    displayDiscussions.forEach((discussion, index) => {
      const platformIcon = getPlatformIconForLabel(discussion.platform);
      const replyIcon = getReplyIcon(discussion.replies);
      
      response += `#### ${platformIcon} ${index + 1}. ${discussion.title}\n`;
      response += `**平台**: ${getPlatformLabel(discussion.platform)} | **回复数**: ${discussion.replies} ${replyIcon}\n`;
      
      // 格式化时间
      const lastActivity = new Date(discussion.lastActivity);
      const timeAgo = getTimeAgo(lastActivity);
      response += `**最后活动**: ${lastActivity.toLocaleDateString()} (${timeAgo})\n`;
      response += `**链接**: ${discussion.url}\n\n`;
    });
    
    if (data.discussions.length > maxDiscussions) {
      response += `💬 还有 ${data.discussions.length - maxDiscussions} 个相关讨论。\n\n`;
    }
  }

  // 相关Issues
  if (data.relatedIssues && data.relatedIssues.length > 0) {
    // 按状态和创建时间排序
    const sortedIssues = data.relatedIssues.sort((a, b) => {
      if (a.state !== b.state) {
        return a.state === 'open' ? -1 : 1; // open issues first
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    const maxIssues = opts.compact ? 3 : sortedIssues.length;
    const displayIssues = sortedIssues.slice(0, maxIssues);
    
    const issueIcon = opts.visualEnhancement ? '🐛' : '';
    response += `### ${issueIcon} ${t.relatedIssuesSection} (${data.relatedIssues.length} 个)\n\n`;
    
    displayIssues.forEach((issue, index) => {
      const stateIcon = issue.state === 'open' ? '🟢' : '🔴';
      const stateText = issue.state === 'open' ? '开放' : '已关闭';
      
      response += `#### ${stateIcon} ${index + 1}. ${issue.title}\n`;
      response += `**编号**: #${issue.number} | **状态**: ${stateText}\n`;
      
      const createdAt = new Date(issue.createdAt);
      const timeAgo = getTimeAgo(createdAt);
      response += `**创建时间**: ${createdAt.toLocaleDateString()} (${timeAgo})\n`;
      response += `**链接**: ${issue.url}\n`;

      if (issue.labels && issue.labels.length > 0) {
        const maxLabels = opts.compact ? 3 : issue.labels.length;
        const displayLabels = issue.labels.slice(0, maxLabels).join(', ');
        response += `**标签**: ${displayLabels}`;
        if (issue.labels.length > maxLabels) {
          response += ` (+${issue.labels.length - maxLabels})`;
        }
        response += '\n';
      }
      response += '\n';
    });
    
    if (data.relatedIssues.length > maxIssues) {
      response += `🐛 还有 ${data.relatedIssues.length - maxIssues} 个相关Issues。\n\n`;
    }
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n🌐 **社区搜索**: 耗时 ${metadata.searchTime || 0}ms`;
    if (metadata.sources) {
      response += ` | 数据源: ${metadata.sources.join(', ')}`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 获取投票数图标
 */
function getVoteIcon(votes: number): string {
  if (votes >= 50) return '💯';
  if (votes >= 20) return '🔥';
  if (votes >= 10) return '👍';
  if (votes >= 5) return '❤️';
  return '👌';
}

/**
 * 获取回复数图标
 */
function getReplyIcon(replies: number): string {
  if (replies >= 100) return '💬💬💬';
  if (replies >= 20) return '💬💬';
  if (replies >= 5) return '💬';
  return '🗨️';
}

/**
 * 获取平台图标（用于标签）
 */
function getPlatformIconForLabel(platform: string): string {
  const icons: { [key: string]: string } = {
    'github': '🐙',
    'discord': '💬',
    'reddit': '🤖',
    'stackoverflow': '📚',
    'forum': '💻'
  };
  return icons[platform.toLowerCase()] || '🌐';
}

/**
 * 获取时间距离现在的描述
 */
function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  
  if (diffDays > 0) return `${diffDays}天前`;
  if (diffHours > 0) return `${diffHours}小时前`;
  if (diffMinutes > 0) return `${diffMinutes}分钟前`;
  return '刚才';
}

/**
 * 辅助函数 - 获取严重性标签
 */
function getSeverityLabel(severity: string): string {
  const labels: { [key: string]: string } = {
    'critical': '🚨 严重',
    'high': '🔴 高',
    'medium': '🟡 中',
    'low': '🟢 低'
  };
  return labels[severity] || severity;
}

/**
 * 辅助函数 - 获取影响标签
 */
function getImpactLabel(impact: string): string {
  const labels: { [key: string]: string } = {
    'breaking': '💥 破坏性',
    'compatible': '✅ 兼容',
    'enhancement': '✨ 增强',
    'high': '🔴 高',
    'medium': '🟡 中',
    'low': '🟢 低'
  };
  return labels[impact] || impact;
}

/**
 * 辅助函数 - 获取变更类型标签
 */
function getChangeTypeLabel(type: string): string {
  const labels: { [key: string]: string } = {
    'added': '✅ 新增',
    'modified': '🔄 修改',
    'deprecated': '⚠️ 弃用',
    'removed': '❌ 移除'
  };
  return labels[type] || type;
}

/**
 * 辅助函数 - 获取平台标签
 */
function getPlatformLabel(platform: string): string {
  const labels: { [key: string]: string } = {
    'github': '🐙 GitHub',
    'discord': '💬 Discord',
    'reddit': '🤖 Reddit',
    'stackoverflow': '📚 Stack Overflow'
  };
  return labels[platform] || platform;
}

// 新增的通用格式化函数
/**
 * 通用格式化器 - 为不同的数据类型提供统一的格式化接口
 */
export function formatUniversalResponse(
  dataType: string,
  data: any,
  metadata: any,
  options: FormatOptions = {}
): string {
  switch (dataType) {
    case 'documentation':
      return formatDocumentationResponse(data, metadata, options);
    case 'codeExamples':
      return formatCodeExamplesResponse(data, metadata, options);
    case 'apiReference':
      return formatApiReferenceResponse(data, metadata, options);
    case 'buildIssue':
      return formatBuildIssueResponse(data, metadata, options);
    case 'migrationGuide':
      return formatMigrationGuideResponse(data, metadata, options);
    case 'platformInfo':
      return formatPlatformInfoResponse(data, metadata, options);
    case 'codeAnalysis':
      return formatCodeAnalysisResponse(data, metadata, options);
    case 'versionComparison':
      return formatVersionComparisonResponse(data, metadata, options);
    case 'bestPractices':
      return formatBestPracticesResponse(data, metadata, options);
    case 'community':
      return formatCommunityResponse(data, metadata, options);
    default:
      return '❌ 不支持的数据类型。';
  }
}

/**
 * 批量格式化 - 处理多个数据源的结果
 */
export function formatBatchResponse(
  results: Array<{ type: string; data: any; metadata: any }>,
  options: FormatOptions = {}
): string {
  if (!results || results.length === 0) {
    return '❌ 没有找到任何结果。';
  }
  
  const opts = {
    language: 'zh' as const,
    maxLength: 5000,
    compact: true, // 批量模式默认紧凑
    visualEnhancement: true,
    ...options
  };
  
  let response = `🔍 **综合搜索结果** (${results.length} 个数据源)\n\n`;
  
  results.forEach((result, index) => {
    const typeIcon = getDataTypeIcon(result.type);
    response += `## ${typeIcon} ${index + 1}. ${getDataTypeLabel(result.type)}\n`;
    
    // 传递紧凑选项
    const formattedResult = formatUniversalResponse(
      result.type, 
      result.data, 
      result.metadata, 
      { ...opts, includeMetadata: false }
    );
    
    // 移除标题行，避免重复
    const contentWithoutTitle = formattedResult.replace(/^[^\n]*\n\n/, '');
    response += contentWithoutTitle + '\n\n---\n\n';
  });
  
  // 添加总的元数据
  const totalTime = results.reduce((sum, r) => sum + (r.metadata?.searchTime || 0), 0);
  response += `📊 **总计**: 耗时 ${totalTime}ms | 数据源 ${results.length} 个\n`;
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（综合响应已截断）');
  }
  
  return response;
}

/**
 * 获取数据类型图标
 */
function getDataTypeIcon(type: string): string {
  const icons: { [key: string]: string } = {
    'documentation': '📚',
    'codeExamples': '💻',
    'apiReference': '📖',
    'buildIssue': '🔧',
    'migrationGuide': '🔄',
    'platformInfo': '🎯',
    'codeAnalysis': '🔍',
    'versionComparison': '🔄',
    'bestPractices': '💡',
    'community': '🌐'
  };
  return icons[type] || '📜';
}

/**
 * 获取数据类型标签
 */
function getDataTypeLabel(type: string): string {
  const labels: { [key: string]: string } = {
    'documentation': '文档搜索',
    'codeExamples': '代码示例',
    'apiReference': 'API参考',
    'buildIssue': '构建问题',
    'migrationGuide': '迁移指南',
    'platformInfo': '平台信息',
    'codeAnalysis': '代码分析',
    'versionComparison': '版本对比',
    'bestPractices': '最佳实践',
    'community': '社区解决方案'
  };
  return labels[type] || type;
}

// 添加全局设置函数
/**
 * 设置全局格式化选项
 */
export function setGlobalFormatOptions(options: Partial<FormatOptions>): void {
  // 这里可以实现全局配置的逻辑
  console.log('🎨 全局格式化选项已更新:', options);
}

/**
 * 格式化语义搜索响应
 */
export function formatSemanticSearchResponse(
  data: any,
  metadata: any,
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 4000,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  if (!data) {
    return '❌ 语义搜索失败。请检查查询参数。';
  }

  let response = `🧠 **Axmol 语义搜索结果**\n\n`;
  
  // 添加搜索置信度
  if (data.confidence !== undefined) {
    const confidenceBar = generateRelevanceBar(data.confidence * 10);
    response += `**搜索置信度**: ${(data.confidence * 100).toFixed(1)}% ${confidenceBar}\n\n`;
  }
  
  // 格式化资源结果
  if (data.resources && data.resources.length > 0) {
    response += formatDocumentationResponse(data.resources, metadata, { ...opts, compact: true });
    response += '\n';
  }
  
  // 智能建议
  if (data.suggestions && data.suggestions.length > 0) {
    const suggestionIcon = opts.visualEnhancement ? '💡' : '';
    response += `### ${suggestionIcon} 智能建议\n`;
    data.suggestions.forEach((suggestion: string, index: number) => {
      response += `  ${index + 1}. ${suggestion}\n`;
    });
    response += '\n';
  }
  
  // 相关概念
  if (data.relatedConcepts && data.relatedConcepts.length > 0) {
    const conceptIcon = opts.visualEnhancement ? '🔗' : '';
    response += `### ${conceptIcon} 相关概念\n`;
    data.relatedConcepts.forEach((concept: string) => {
      response += `- ${concept}\n`;
    });
    response += '\n';
  }
  
  // 学习路径
  if (data.learningPath && data.learningPath.length > 0) {
    const pathIcon = opts.visualEnhancement ? '🛤️' : '';
    response += `### ${pathIcon} 推荐学习路径\n`;
    data.learningPath.forEach((step: string, index: number) => {
      response += `  ${index + 1}. ${step}\n`;
    });
    response += '\n';
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n🧠 **语义分析**: 耗时 ${metadata.searchTime || 0}ms | 意图: ${metadata.intent || '未知'} | 置信度: ${((metadata.confidence || 0) * 100).toFixed(1)}%\n`;
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 格式化知识库管理响应
 */
export function formatKnowledgeBaseResponse(
  data: any,
  metadata: any,
  options: FormatOptions = {}
): string {
  const opts = {
    language: 'zh' as const,
    maxLength: 3000,
    includeMetadata: true,
    compact: false,
    visualEnhancement: true,
    ...options
  };
  
  if (!data) {
    return '❌ 知识库操作失败。';
  }

  let response = `📚 **Axmol 知识库管理**\n\n`;
  
  // 根据不同的数据类型格式化
  if (Array.isArray(data)) {
    // 搜索结果（节点数组）
    response += `**搜索结果** (${data.length} 个节点)\n\n`;
    
    const maxNodes = opts.compact ? 5 : data.length;
    data.slice(0, maxNodes).forEach((node: any, index: number) => {
      const typeIcon = getKnowledgeNodeIcon(node.type);
      const importanceBar = generateRelevanceBar(node.importance * 10);
      
      response += `### ${typeIcon} ${index + 1}. ${node.title}\n`;
      response += `**类型**: ${getKnowledgeNodeTypeLabel(node.type)} | **类别**: ${node.category}\n`;
      response += `**重要性**: ${(node.importance * 10).toFixed(1)}/10 ${importanceBar}\n`;
      
      const maxContentLength = opts.compact ? 100 : 200;
      const content = smartTruncate(node.content, maxContentLength);
      response += `**内容**: ${content}\n`;
      
      if (node.tags && node.tags.length > 0) {
        const maxTags = opts.compact ? 3 : node.tags.length;
        const displayTags = node.tags.slice(0, maxTags).join(', ');
        response += `**标签**: ${displayTags}`;
        if (node.tags.length > maxTags) {
          response += ` (+${node.tags.length - maxTags})`;
        }
        response += '\n';
      }
      
      response += '\n';
    });
    
    if (data.length > maxNodes) {
      response += `💡 显示了前 ${maxNodes} 个节点，共 ${data.length} 个节点可用。\n`;
    }
    
  } else if (data.totalNodes !== undefined) {
    // 统计信息
    response += `**知识库统计**\n\n`;
    response += `📊 **总体统计**\n`;
    response += `- 总节点数: ${data.totalNodes}\n`;
    response += `- 总关系数: ${data.totalRelationships}\n`;
    response += `- 平均重要性: ${(data.averageImportance * 10).toFixed(1)}/10\n`;
    response += `- 最后索引: ${new Date(data.lastIndexed).toLocaleString()}\n\n`;
    
    if (data.categoryCounts) {
      response += `📂 **类别分布**\n`;
      Object.entries(data.categoryCounts).forEach(([category, count]) => {
        response += `- ${category}: ${count} 个节点\n`;
      });
      response += '\n';
    }
    
    if (data.typeCounts) {
      response += `🏷️ **类型分布**\n`;
      Object.entries(data.typeCounts).forEach(([type, count]) => {
        const typeLabel = getKnowledgeNodeTypeLabel(type);
        response += `- ${typeLabel}: ${count} 个节点\n`;
      });
      response += '\n';
    }
    
    if (data.coverage) {
      response += `📈 **覆盖率统计**\n`;
      response += `- API 文档: ${data.coverage.api} 个\n`;
      response += `- 代码示例: ${data.coverage.examples} 个\n`;
      response += `- 教程: ${data.coverage.tutorials} 个\n`;
      response += `- 概念: ${data.coverage.concepts} 个\n\n`;
    }
    
  } else if (typeof data === 'string') {
    // 导出数据或消息
    if (metadata?.format) {
      response += `**导出数据** (${metadata.format.toUpperCase()} 格式)\n\n`;
      const maxExportLength = opts.compact ? 500 : 1000;
      const exportData = smartTruncate(data, maxExportLength, '\n\n...（导出数据已截断）');
      response += `\`\`\`${metadata.format}\n${exportData}\n\`\`\`\n`;
    } else {
      response += data;
    }
  } else if (data.message) {
    // 操作消息
    response += `✅ ${data.message}\n`;
  }
  
  // 元数据
  if (opts.includeMetadata && metadata) {
    response += `---\n📚 **知识库操作**: 耗时 ${metadata.searchTime || 0}ms`;
    if (metadata.sources) {
      response += ` | 数据源: ${metadata.sources.join(', ')}`;
    }
    response += '\n';
  }
  
  // 智能截断
  if (response.length > opts.maxLength) {
    response = smartTruncate(response, opts.maxLength, '\n\n...（响应已截断）');
  }

  return response;
}

/**
 * 获取知识节点类型图标
 */
function getKnowledgeNodeIcon(type: string): string {
  const icons: { [key: string]: string } = {
    'concept': '💡',
    'api': '📖',
    'example': '💻',
    'tutorial': '📚',
    'best_practice': '⭐'
  };
  return icons[type] || '📄';
}

/**
 * 获取知识节点类型标签
 */
function getKnowledgeNodeTypeLabel(type: string): string {
  const labels: { [key: string]: string } = {
    'concept': '概念',
    'api': 'API',
    'example': '示例',
    'tutorial': '教程',
    'best_practice': '最佳实践'
  };
  return labels[type] || type;
}

// 添加新的导出选项和兼容性函数
export {
  FormatOptions,
  smartTruncate,
  calculateRelevanceScore,
  sortByRelevance,
  generateRelevanceBar,
  getResourceIcon,
  getLanguageIcon,
  getPlatformIcon,
  generateQualityBar,
  getSeverityIcon,
  getImpactIcon,
  getPriorityIcon,
  getCategoryIcon,
  getVoteIcon,
  getReplyIcon,
  getTimeAgo
};




