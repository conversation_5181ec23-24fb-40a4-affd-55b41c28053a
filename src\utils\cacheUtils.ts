/**
 * 增强的多层缓存工具类
 * 提供内存缓存、磁盘持久化、智能预取和压缩功能
 */

import fs from 'fs-extra';
import * as path from 'path';
import { promisify } from 'util';
import { gzip, gunzip } from 'zlib';
import { CacheEntry, CacheOptions } from '../types/index.js';

const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

// 增强的缓存条目接口
interface EnhancedCacheEntry<T> extends CacheEntry<T> {
  accessCount: number;
  lastAccessed: number;
  compressed: boolean;
  tags: string[];
  priority: 'low' | 'medium' | 'high';
  size: number;
}

// 缓存统计信息
interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  size: number;
  maxSize: number;
  hitRate: number;
  memoryUsage: number;
  oldestEntry: string | null;
  newestEntry: string | null;
  mostAccessed: string | null;
}

// 缓存策略
type EvictionStrategy = 'lru' | 'lfu' | 'fifo' | 'priority';
type CompressionStrategy = 'auto' | 'always' | 'never';

export class EnhancedCacheUtils<T = any> {
  private l1Cache: Map<string, EnhancedCacheEntry<T>> = new Map(); // 内存缓存（L1）
  private l2Cache: Map<string, string> = new Map(); // 磁盘缓存索引（L2）
  private options: Required<CacheOptions & {
    evictionStrategy: EvictionStrategy;
    compressionStrategy: CompressionStrategy;
    l1MaxSize: number;
    l2MaxSize: number;
    compressionThreshold: number;
    prefetchEnabled: boolean;
  }>;
  private cacheDir: string;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private prefetchQueue: Set<string> = new Set();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    size: 0,
    maxSize: 0,
    hitRate: 0,
    memoryUsage: 0,
    oldestEntry: null,
    newestEntry: null,
    mostAccessed: null
  };

  constructor(options?: CacheOptions & {
    evictionStrategy?: EvictionStrategy;
    compressionStrategy?: CompressionStrategy;
    l1MaxSize?: number;
    l2MaxSize?: number;
    compressionThreshold?: number;
    prefetchEnabled?: boolean;
  }) {
    this.options = {
      ttl: 30 * 60 * 1000, // 默认30分钟
      maxSize: 1000, // 默认最大1000条
      enablePersistence: false,
      evictionStrategy: 'lru',
      compressionStrategy: 'auto',
      l1MaxSize: 200, // L1缓存最大200条
      l2MaxSize: 800, // L2缓存最大800条
      compressionThreshold: 1024, // 1KB以上启用压缩
      prefetchEnabled: true,
      ...options
    };
    
    this.stats.maxSize = this.options.maxSize;

    this.cacheDir = path.join(process.cwd(), 'cache');
    
    if (this.options.enablePersistence) {
      this.initPersistence();
    }

    // 启动定期清理
    this.startCleanup();
    this.updateStats();
  }

  /**
   * 增强的缓存设置
   */
  async set(key: string, data: T, options?: {
    ttl?: number;
    tags?: string[];
    priority?: 'low' | 'medium' | 'high';
    forceL1?: boolean;
  }): Promise<void> {
    const now = Date.now();
    const serializedData = JSON.stringify(data);
    const dataSize = Buffer.byteLength(serializedData, 'utf8');
    
    // 决定是否压缩
    const shouldCompress = this.shouldCompress(dataSize);
    const finalData = shouldCompress ? await this.compressData(serializedData) : data;
    
    const entry: EnhancedCacheEntry<T> = {
      data: finalData,
      timestamp: now,
      ttl: options?.ttl || this.options.ttl,
      key,
      accessCount: 0,
      lastAccessed: now,
      compressed: shouldCompress,
      tags: options?.tags || [],
      priority: options?.priority || 'medium',
      size: dataSize
    };

    // 智能缓存层选择
    const shouldStoreInL1 = this.shouldStoreInL1(entry, options?.forceL1);
    
    if (shouldStoreInL1) {
      await this.setL1(key, entry);
    } else {
      await this.setL2(key, entry);
    }
    
    this.stats.sets++;
    this.updateStats();
    
    console.log(`💾 增强缓存已设置: ${key} (${shouldStoreInL1 ? 'L1' : 'L2'}, ${shouldCompress ? '压缩' : '未压缩'}, TTL: ${entry.ttl}ms)`);
  }
  
  /**
   * 设置L1缓存
   */
  private async setL1(key: string, entry: EnhancedCacheEntry<T>): Promise<void> {
    // 检查L1缓存大小限制
    if (this.l1Cache.size >= this.options.l1MaxSize) {
      await this.evictFromL1();
    }
    
    this.l1Cache.set(key, entry);
    
    // 如果启用持久化，同时保存到L2
    if (this.options.enablePersistence) {
      await this.setL2(key, entry, false);
    }
  }
  
  /**
   * 设置L2缓存
   */
  private async setL2(key: string, entry: EnhancedCacheEntry<T>, removeFromL1: boolean = true): Promise<void> {
    if (removeFromL1) {
      // 从 L1 移除（如果存在）
      this.l1Cache.delete(key);
    }
    
    // 检查L2缓存大小限制
    if (this.l2Cache.size >= this.options.l2MaxSize) {
      await this.evictFromL2();
    }
    
    if (this.options.enablePersistence) {
      await this.persistEntry(key, entry);
      this.l2Cache.set(key, this.getFilename(key));
    }
  }
  
  /**
   * 判断是否应该存储在L1缓存
   */
  private shouldStoreInL1(entry: EnhancedCacheEntry<T>, forceL1?: boolean): boolean {
    if (forceL1) return true;
    
    // 根据优先级和大小决定
    if (entry.priority === 'high') return true;
    if (entry.priority === 'low') return false;
    
    // 中等优先级根据大小决定
    return entry.size < this.options.compressionThreshold;
  }
  
  /**
   * 判断是否应该压缩
   */
  private shouldCompress(size: number): boolean {
    switch (this.options.compressionStrategy) {
      case 'always': return true;
      case 'never': return false;
      case 'auto': return size > this.options.compressionThreshold;
      default: return false;
    }
  }
  
  /**
   * 压缩数据
   */
  private async compressData(data: string): Promise<any> {
    try {
      const buffer = Buffer.from(data, 'utf8');
      const compressed = await gzipAsync(buffer);
      return {
        __compressed: true,
        data: compressed.toString('base64')
      };
    } catch (error) {
      console.warn('⚠️ 数据压缩失败，使用原始数据:', error);
      return JSON.parse(data);
    }
  }
  
  /**
   * 解压数据
   */
  private async decompressData(compressedData: any): Promise<any> {
    try {
      if (compressedData && compressedData.__compressed) {
        const buffer = Buffer.from(compressedData.data, 'base64');
        const decompressed = await gunzipAsync(buffer);
        return JSON.parse(decompressed.toString('utf8'));
      }
      return compressedData;
    } catch (error) {
      console.warn('⚠️ 数据解压失败:', error);
      return compressedData;
    }
  }

  /**
   * 增强的缓存获取
   */
  async get(key: string, options?: {
    promoteTo?: 'l1' | 'l2';
    prefetchRelated?: boolean;
  }): Promise<T | null> {
    const startTime = Date.now();
    let entry: EnhancedCacheEntry<T> | undefined;
    let cacheLevel = '';
    
    // 1. 尝试从L1缓存获取
    entry = this.l1Cache.get(key);
    if (entry) {
      cacheLevel = 'L1';
    }
    
    // 2. 如果L1没有，尝试从L2缓存获取
    if (!entry && this.l2Cache.has(key)) {
      entry = await this.loadFromL2(key);
      if (entry) {
        cacheLevel = 'L2';
        // 根据访问模式决定是否提升到L1
        if (this.shouldPromoteToL1(entry) || options?.promoteTo === 'l1') {
          await this.promoteToL1(key, entry);
          cacheLevel = 'L2→L1';
        }
      }
    }
    
    if (!entry) {
      this.stats.misses++;
      console.log(`❌ 缓存未命中: ${key}`);
      return null;
    }
    
    // 3. 检查是否过期
    if (this.isExpired(entry)) {
      console.log(`⏰ 缓存已过期: ${key}`);
      await this.delete(key);
      this.stats.misses++;
      return null;
    }
    
    // 4. 更新访问统计
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;
    
    // 5. 智能预取
    if (this.options.prefetchEnabled && options?.prefetchRelated) {
      this.schedulePrefetch(key, entry.tags);
    }
    
    // 6. 解压数据（如果需要）
    const finalData = entry.compressed ? await this.decompressData(entry.data) : entry.data;
    
    const accessTime = Date.now() - startTime;
    console.log(`✅ 缓存命中: ${key} (${cacheLevel}, ${accessTime}ms, 访问${entry.accessCount}次)`);
    
    return finalData;
  }
  
  /**
   * 从L2加载缓存项
   */
  private async loadFromL2(key: string): Promise<EnhancedCacheEntry<T> | undefined> {
    const filename = this.l2Cache.get(key);
    if (!filename) return undefined;
    
    try {
      const filepath = path.join(this.cacheDir, filename);
      if (await fs.pathExists(filepath)) {
        const entry = await fs.readJson(filepath) as EnhancedCacheEntry<T>;
        return entry;
      }
    } catch (error) {
      console.error(`❌ 从L2加载缓存项失败 ${key}:`, error);
      // 清理无效索引
      this.l2Cache.delete(key);
    }
    
    return undefined;
  }
  
  /**
   * 判断是否应该提升到L1
   */
  private shouldPromoteToL1(entry: EnhancedCacheEntry<T>): boolean {
    // 高优先级或高频访问的数据提升到L1
    return entry.priority === 'high' || 
           entry.accessCount > 3 || 
           (Date.now() - entry.lastAccessed) < 5 * 60 * 1000; // 5分钟内访问过
  }
  
  /**
   * 提升到L1缓存
   */
  private async promoteToL1(key: string, entry: EnhancedCacheEntry<T>): Promise<void> {
    // 检查L1空间
    if (this.l1Cache.size >= this.options.l1MaxSize) {
      await this.evictFromL1();
    }
    
    this.l1Cache.set(key, entry);
    console.log(`⬆️ 缓存项提升到L1: ${key}`);
  }
  
  /**
   * 智能预取调度
   */
  private schedulePrefetch(currentKey: string, tags: string[]): void {
    if (this.prefetchQueue.size > 10) return; // 限制预取队列大小
    
    // 基于标签查找相关缓存项
    for (const [key, entry] of this.l2Cache.entries()) {
      if (key !== currentKey && !this.l1Cache.has(key)) {
        // 这里可以实现更复杂的相关性算法
        this.prefetchQueue.add(key);
        if (this.prefetchQueue.size >= 5) break;
      }
    }
    
    // 异步执行预取
    setTimeout(() => this.executePrefetch(), 100);
  }
  
  /**
   * 执行预取
   */
  private async executePrefetch(): Promise<void> {
    const keysToFetch = Array.from(this.prefetchQueue).slice(0, 3);
    this.prefetchQueue.clear();
    
    for (const key of keysToFetch) {
      try {
        await this.get(key); // 预取到L1
      } catch (error) {
        // 静默失败，不影响主流程
      }
    }
  }

  /**
   * 删除缓存项
   */
  async delete(key: string): Promise<boolean> {
    let deleted = false;
    
    // 从L1删除
    if (this.l1Cache.delete(key)) {
      deleted = true;
    }
    
    // 从L2删除
    if (this.l2Cache.delete(key)) {
      deleted = true;
    }
    
    // 删除持久化文件
    if (this.options.enablePersistence) {
      await this.deletePersistentEntry(key);
    }
    
    if (deleted) {
      this.stats.deletes++;
      console.log(`🗑️ 缓存已删除: ${key}`);
    }
    
    return deleted;
  }

  /**
   * 检查缓存项是否存在且未过期
   */
  async has(key: string): Promise<boolean> {
    // 优化：检查存在性而不实际获取数据
    let entry = this.l1Cache.get(key);
    
    if (!entry && this.l2Cache.has(key)) {
      entry = await this.loadFromL2(key);
    }
    
    return entry !== undefined && !this.isExpired(entry);
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    this.l1Cache.clear();
    this.l2Cache.clear();
    this.prefetchQueue.clear();

    if (this.options.enablePersistence) {
      await this.clearPersistentCache();
    }
    
    // 重置统计
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      size: 0,
      maxSize: this.options.maxSize,
      hitRate: 0,
      memoryUsage: 0,
      oldestEntry: null,
      newestEntry: null,
      mostAccessed: null
    };

    console.log('🧹 增强缓存已清空');
  }

  /**
   * 获取增强的缓存统计信息
   */
  getEnhancedStats(): CacheStats & {
    l1Size: number;
    l2Size: number;
    compressionRatio: number;
    averageAccessTime: number;
    topAccessed: Array<{ key: string; count: number }>;
  } {
    this.updateStats();
    
    // 计算压缩比例
    const compressedEntries = Array.from(this.l1Cache.values()).filter(e => e.compressed).length;
    const totalEntries = this.l1Cache.size;
    const compressionRatio = totalEntries > 0 ? compressedEntries / totalEntries : 0;
    
    // 计算内存使用量
    let memoryUsage = 0;
    for (const entry of this.l1Cache.values()) {
      memoryUsage += entry.size;
    }
    
    // 获取访问次数最多的缓存项
    const topAccessed = Array.from(this.l1Cache.entries())
      .sort((a, b) => b[1].accessCount - a[1].accessCount)
      .slice(0, 5)
      .map(([key, entry]) => ({ key, count: entry.accessCount }));
    
    return {
      ...this.stats,
      l1Size: this.l1Cache.size,
      l2Size: this.l2Cache.size,
      compressionRatio,
      averageAccessTime: 0, // 需要跟踪访问时间
      topAccessed,
      memoryUsage
    };
  }
  
  /**
   * 更新统计信息
   */
  private updateStats(): void {
    let oldest: [string, EnhancedCacheEntry<T>] | null = null;
    let newest: [string, EnhancedCacheEntry<T>] | null = null;
    let mostAccessed: [string, EnhancedCacheEntry<T>] | null = null;
    
    for (const [key, entry] of this.l1Cache.entries()) {
      if (!oldest || entry.timestamp < oldest[1].timestamp) {
        oldest = [key, entry];
      }
      if (!newest || entry.timestamp > newest[1].timestamp) {
        newest = [key, entry];
      }
      if (!mostAccessed || entry.accessCount > mostAccessed[1].accessCount) {
        mostAccessed = [key, entry];
      }
    }
    
    this.stats.size = this.l1Cache.size + this.l2Cache.size;
    this.stats.oldestEntry = oldest ? oldest[0] : null;
    this.stats.newestEntry = newest ? newest[0] : null;
    this.stats.mostAccessed = mostAccessed ? mostAccessed[0] : null;
    this.stats.hitRate = this.stats.hits + this.stats.misses > 0 ? 
      this.stats.hits / (this.stats.hits + this.stats.misses) : 0;
  }

  /**
   * 获取所有缓存键
   */
  getKeys(): string[] {
    const l1Keys = Array.from(this.l1Cache.keys());
    const l2Keys = Array.from(this.l2Cache.keys());
    return [...new Set([...l1Keys, ...l2Keys])];
  }

  /**
   * 获取未过期的缓存键
   */
  async getValidKeys(): Promise<string[]> {
    const validKeys: string[] = [];
    
    // 检查L1
    for (const [key, entry] of this.l1Cache.entries()) {
      if (!this.isExpired(entry)) {
        validKeys.push(key);
      }
    }
    
    // 检查L2（但不重复加载）
    for (const key of this.l2Cache.keys()) {
      if (!validKeys.includes(key)) {
        const entry = await this.loadFromL2(key);
        if (entry && !this.isExpired(entry)) {
          validKeys.push(key);
        }
      }
    }
    
    return validKeys;
  }
  
  /**
   * 根据标签获取缓存键
   */
  getKeysByTags(tags: string[]): string[] {
    const matchingKeys: string[] = [];
    
    for (const [key, entry] of this.l1Cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        matchingKeys.push(key);
      }
    }
    
    return matchingKeys;
  }
  
  /**
   * 批量删除标签相关的缓存
   */
  async deleteByTags(tags: string[]): Promise<number> {
    const keysToDelete = this.getKeysByTags(tags);
    let deletedCount = 0;
    
    for (const key of keysToDelete) {
      if (await this.delete(key)) {
        deletedCount++;
      }
    }
    
    console.log(`🗑️ 按标签删除了 ${deletedCount} 个缓存项`);
    return deletedCount;
  }

  /**
   * 增强的缓存清理
   */
  async cleanup(): Promise<number> {
    let cleanedCount = 0;
    
    // 清理L1缓存
    for (const [key, entry] of this.l1Cache.entries()) {
      if (this.isExpired(entry)) {
        this.l1Cache.delete(key);
        cleanedCount++;
      }
    }
    
    // 清理L2缓存
    const l2Keys = Array.from(this.l2Cache.keys());
    for (const key of l2Keys) {
      const entry = await this.loadFromL2(key);
      if (!entry || this.isExpired(entry)) {
        this.l2Cache.delete(key);
        if (this.options.enablePersistence) {
          await this.deletePersistentEntry(key);
        }
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🧹 增强清理了 ${cleanedCount} 个过期缓存项`);
      this.updateStats();
    }
    
    return cleanedCount;
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(entry: EnhancedCacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * 从L1缓存驱逐
   */
  private async evictFromL1(): Promise<void> {
    const evictKey = this.selectEvictionCandidate(this.l1Cache, this.options.evictionStrategy);
    if (!evictKey) return;
    
    const entry = this.l1Cache.get(evictKey);
    this.l1Cache.delete(evictKey);
    
    // 将驱逐的项移动到L2（如果值得保留）
    if (entry && this.shouldKeepInL2(entry)) {
      await this.setL2(evictKey, entry, false);
      console.log(`🔄 L1驱逐并移动到L2: ${evictKey}`);
    } else {
      console.log(`🗑️ L1驱逐: ${evictKey}`);
    }
    
    this.stats.evictions++;
  }
  
  /**
   * 从L2缓存驱逐
   */
  private async evictFromL2(): Promise<void> {
    const evictKey = this.selectEvictionCandidate(this.l2Cache, this.options.evictionStrategy);
    if (!evictKey) return;
    
    this.l2Cache.delete(evictKey);
    
    // 删除磁盘文件
    if (this.options.enablePersistence) {
      await this.deletePersistentEntry(evictKey);
    }
    
    console.log(`🗑️ L2驱逐: ${evictKey}`);
    this.stats.evictions++;
  }
  
  /**
   * 选择驱逐候选项
   */
  private selectEvictionCandidate(cache: Map<string, any>, strategy: EvictionStrategy): string | null {
    if (cache.size === 0) return null;
    
    const entries = Array.from(cache.entries());
    
    switch (strategy) {
      case 'lru': // 最近最少使用
        return entries.reduce((oldest, [key, entry]) => {
          if (!oldest || (entry.lastAccessed && entry.lastAccessed < oldest[1].lastAccessed)) {
            return [key, entry];
          }
          return oldest;
        })[0];
        
      case 'lfu': // 最低频率使用
        return entries.reduce((lowest, [key, entry]) => {
          if (!lowest || (entry.accessCount && entry.accessCount < lowest[1].accessCount)) {
            return [key, entry];
          }
          return lowest;
        })[0];
        
      case 'fifo': // 先进先出
        return entries.reduce((oldest, [key, entry]) => {
          if (!oldest || (entry.timestamp && entry.timestamp < oldest[1].timestamp)) {
            return [key, entry];
          }
          return oldest;
        })[0];
        
      case 'priority': // 优先级最低
        const priorityOrder = { 'low': 0, 'medium': 1, 'high': 2 };
        return entries.reduce((lowest, [key, entry]) => {
          const currentPriority = priorityOrder[(entry.priority || 'medium') as keyof typeof priorityOrder];
          const lowestPriority = priorityOrder[(lowest?.[1]?.priority || 'medium') as keyof typeof priorityOrder];
          if (!lowest || currentPriority < lowestPriority) {
            return [key, entry];
          }
          return lowest;
        })[0];
        
      default:
        return entries[0][0]; // 默认返回第一个
    }
  }
  
  /**
   * 判断是否应该在L2中保留
   */
  private shouldKeepInL2(entry: EnhancedCacheEntry<T>): boolean {
    // 高优先级或访问频繁的数据应该保留
    return entry.priority === 'high' || 
           entry.accessCount > 1 || 
           entry.tags.length > 0; // 有标签的数据可能有相关性
  }

  /**
   * 初始化持久化
   */
  private async initPersistence(): Promise<void> {
    try {
      await fs.ensureDir(this.cacheDir);
      console.log(`📁 缓存目录已创建: ${this.cacheDir}`);
    } catch (error) {
      console.error('❌ 初始化缓存目录失败:', error);
      this.options.enablePersistence = false;
    }
  }

  /**
   * 持久化缓存项
   */
  private async persistEntry(key: string, entry: EnhancedCacheEntry<T>): Promise<void> {
    try {
      const filename = this.getFilename(key);
      const filepath = path.join(this.cacheDir, filename);
      
      // 优化：使用异步写入并添加错误重试
      await this.writeWithRetry(filepath, entry, 3);
    } catch (error) {
      console.error(`❌ 持久化缓存项失败 ${key}:`, error);
    }
  }
  
  /**
   * 带重试的写入
   */
  private async writeWithRetry(filepath: string, data: any, retries: number): Promise<void> {
    for (let i = 0; i < retries; i++) {
      try {
        await fs.writeJson(filepath, data);
        return;
      } catch (error) {
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 100 * (i + 1))); // 指数退避
      }
    }
  }

  /**
   * 删除持久化的缓存项
   */
  private async deletePersistentEntry(key: string): Promise<void> {
    try {
      const filename = this.getFilename(key);
      const filepath = path.join(this.cacheDir, filename);
      await fs.remove(filepath);
    } catch (error) {
      console.error(`❌ 删除持久化缓存项失败 ${key}:`, error);
    }
  }

  /**
   * 清空持久化缓存
   */
  private async clearPersistentCache(): Promise<void> {
    try {
      await fs.emptyDir(this.cacheDir);
    } catch (error) {
      console.error('❌ 清空持久化缓存失败:', error);
    }
  }

  /**
   * 获取缓存文件名
   */
  private getFilename(key: string): string {
    // 将键转换为安全的文件名，支持更长的键
    const safeKey = key.replace(/[^a-zA-Z0-9]/g, '_');
    const hash = this.simpleHash(key); // 添加哈希以避免冲突
    return `${safeKey.substring(0, 50)}_${hash}.json`; // 限制文件名长度
  }
  
  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 启动定期清理
   */
  private startCleanup(): void {
    // 每3分钟清理一次过期缓存（更频繁）
    this.cleanupInterval = setInterval(async () => {
      await this.cleanup();
    }, 3 * 60 * 1000);
  }

  /**
   * 停止定期清理
   */
  stopCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 销毁缓存实例
   */
  async destroy(): Promise<void> {
    this.stopCleanup();
    await this.clear();
    console.log('💥 增强缓存实例已销毁');
  }
}

// 保留原始类以保持兼容性
export class CacheUtils<T = any> {
  private enhanced: EnhancedCacheUtils<T>;
  
  constructor(options?: CacheOptions) {
    this.enhanced = new EnhancedCacheUtils(options);
  }
  
  // 兼容方法：使用原始接口
  async set(key: string, data: T, ttl?: number): Promise<void> {
    return this.enhanced.set(key, data, { ttl });
  }
  
  async get(key: string): Promise<T | null> {
    return this.enhanced.get(key);
  }
  
  async delete(key: string): Promise<boolean> {
    return this.enhanced.delete(key);
  }
  
  async has(key: string): Promise<boolean> {
    return this.enhanced.has(key);
  }
  
  async clear(): Promise<void> {
    return this.enhanced.clear();
  }
  
  getStats() {
    const enhanced = this.enhanced.getEnhancedStats();
    return {
      size: enhanced.size,
      maxSize: enhanced.maxSize,
      hitRate: enhanced.hitRate,
      oldestEntry: enhanced.oldestEntry,
      newestEntry: enhanced.newestEntry
    };
  }
  
  getKeys(): string[] {
    return this.enhanced.getKeys();
  }
  
  async getValidKeys(): Promise<string[]> {
    return this.enhanced.getValidKeys();
  }
  
  async cleanup(): Promise<number> {
    return this.enhanced.cleanup();
  }
  
  stopCleanup(): void {
    return this.enhanced.stopCleanup();
  }
  
  async destroy(): Promise<void> {
    return this.enhanced.destroy();
  }
}

// 导出默认缓存实例（兼容版本）
export const defaultCache = new CacheUtils({
  ttl: 30 * 60 * 1000, // 30分钟
  maxSize: 500,
  enablePersistence: true
});

// 导出类型
export type { EnhancedCacheEntry, CacheStats, EvictionStrategy, CompressionStrategy };