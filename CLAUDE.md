# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个为Axmol游戏引擎提供AI支持的MCP（Model Context Protocol）服务器。它提供10个专业工具，帮助开发者获取Axmol官方文档、代码示例、API参考、构建问题解决方案等。

## 核心架构

### 技术栈
- **运行时**: Node.js 18+
- **语言**: TypeScript 5.3.3，ES2022目标，ESNext模块
- **框架**: MCP SDK 0.4.0
- **HTTP客户端**: Axios 1.6.0
- **HTML解析**: Cheerio 1.0.0

### 三层架构
1. **服务层** (`src/services/`): 10个专业服务，每个负责特定功能领域
2. **工具层** (`src/utils/`): 通用工具类，包括缓存、网络、错误处理
3. **数据源管理层**: 优先级数据源管理（官方文档 > GitHub > 社区）

### 10个核心MCP工具
- `search_axmol_documentation` - 搜索官方文档和API参考
- `find_code_examples` - 查找代码示例
- `get_api_reference` - 获取API参考文档
- `solve_build_issue` - 诊断构建问题
- `get_migration_guide` - Cocos2d-x迁移指南
- `find_platform_specific_info` - 平台特定信息
- `analyze_axmol_code` - 代码分析
- `compare_axmol_versions` - 版本对比
- `get_best_practices` - 最佳实践
- `search_community_solutions` - 社区解决方案

## 常用开发命令

### 构建和运行
```bash
# 编译TypeScript
npm run build

# 启动MCP服务器
npm start

# 开发模式运行
npm run dev

# 健康检查
npm run health
```

### 测试
```bash
# 运行所有测试
npm run test:all

# 基本功能测试
npm run test:basic

# MCP功能测试
npm run test:simple

# 最终验证测试
npm run test:verify

# 稳定性测试
npm run test:stability
```

### 代码质量
```bash
# 代码检查
npm run lint

# 自动修复代码风格
npm run lint:fix

# 代码格式化
npm run format

# 检查代码格式
npm run format:check
```

### 清理和维护
```bash
# 清理构建产物
npm run clean

# 完全清理（包括node_modules）
npm run clean:all

# 监控服务状态
npm run monitor

# 验证构建和功能
npm run verify
```

## 文件结构说明

### 核心文件
- `src/index.ts` - MCP服务器主入口，注册所有10个工具
- `dist/index.js` - 编译后的可执行文件
- `tsconfig.json` - TypeScript配置，使用严格模式

### 服务模块 (`src/services/`)
每个服务文件负责一个特定的MCP工具：
- `documentationService.ts` - 文档搜索服务
- `apiReferenceService.ts` - API参考服务
- `codeExampleService.ts` - 代码示例服务
- `buildIssueService.ts` - 构建问题解决服务
- 其他6个专业服务文件

### 工具模块 (`src/utils/`)
- `cacheUtils.ts` - 30分钟TTL文件系统缓存
- `networkUtils.ts` - 网络请求工具，支持重试和超时
- `errorHandler.ts` - 统一错误处理
- `dataSourceManager.ts` - 数据源优先级管理
- `responseFormatter.ts` - 响应格式化工具

### 测试文件
- `test-basic-functionality.js` - 基本功能测试
- `test-simple-mcp.js` - MCP功能测试
- `test-final-verification.js` - 最终验证测试
- `test-stability.js` - 稳定性测试

## 开发指南

### 添加新的MCP工具
1. 在 `src/services/` 创建新的服务文件
2. 在 `src/index.ts` 的工具列表中注册新工具
3. 在 `CallToolRequestSchema` 处理器中添加case分支
4. 在 `responseFormatter.ts` 添加响应格式化函数
5. 添加相应的测试用例

### 缓存策略
- **位置**: `./cache/` 目录
- **TTL**: 30分钟（1800000毫秒）
- **键格式**: `{service}_{query}_{options}`
- **清理**: 自动过期清理

### 数据源优先级
1. 官方API文档 (axmol.dev) - 最高优先级
2. GitHub仓库源码
3. GitHub Wiki
4. 社区讨论
5. 网络搜索 - 最低优先级

### 错误处理
- 使用统一的错误处理器 (`errorHandler.ts`)
- 网络请求支持3次重试，延迟1秒
- 超时设置30秒
- 优雅降级到备用数据源

### 性能优化
- 最大并发请求数: 10个
- 缓存命中率监控
- 内存使用量监控
- 响应时间跟踪

## 环境配置

### 必需环境变量
```bash
NODE_ENV=production
CACHE_TTL=1800000          # 缓存TTL (毫秒)
MAX_CONCURRENT_REQUESTS=10 # 最大并发请求数
TIMEOUT_MS=30000          # 请求超时时间 (毫秒)
```

### 可选环境变量
```bash
RETRY_ATTEMPTS=3          # 重试次数
RETRY_DELAY=1000         # 重试延迟 (毫秒)
LOG_LEVEL=info           # 日志级别
CACHE_DIR=./cache        # 缓存目录
```

## 常见问题排查

### 构建失败
```bash
# 清理并重新构建
npm run clean
npm install
npm run build
```

### MCP工具无响应
```bash
# 检查服务器状态
node dist/index.js --version

# 运行诊断测试
npm run test:verify
```

### 网络连接问题
```bash
# 测试官方文档连接
curl -I https://axmol.dev/manual/latest/
```

### 缓存问题
```bash
# 清理缓存
rm -rf cache/*
```

## 代码约定

### TypeScript配置
- 严格模式启用
- ES2022目标
- ESNext模块系统
- 生成声明文件和源映射

### 代码风格
- 使用ESLint和Prettier
- 2空格缩进
- 单引号字符串
- 尾随逗号

### 导入约定
- 使用ES模块导入 (`import`)
- 相对路径使用 `.js` 扩展名（TypeScript编译要求）
- 按类型分组导入（外部库、服务、工具）

### 错误处理
- 所有异步操作都要有错误处理
- 使用统一的错误格式
- 记录错误日志和执行时间