/**
 * 知识库管理服务
 * 负责知识库的构建、维护、查询和更新
 */

import fs from 'fs-extra';
import * as path from 'path';
import { AxmolResource, SearchOptions, ToolResponse } from '../types/index.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { KnowledgeNode, KnowledgeRelationship } from './semanticSearchService.js';

// 知识库索引
interface KnowledgeIndex {
  concepts: Map<string, string[]>; // 概念 -> 资源ID列表
  categories: Map<string, string[]>; // 类别 -> 资源ID列表
  tags: Map<string, string[]>; // 标签 -> 资源ID列表
  lastUpdated: Date;
}

// 知识库统计
interface KnowledgeStats {
  totalNodes: number;
  totalRelationships: number;
  categoryCounts: Record<string, number>;
  typeCounts: Record<string, number>;
  averageImportance: number;
  lastIndexed: Date;
  coverage: {
    api: number;
    examples: number;
    tutorials: number;
    concepts: number;
  };
}

// 知识库搜索选项
interface KnowledgeSearchOptions extends SearchOptions {
  category?: string;
  nodeType?: string;
  minImportance?: number;
  includeRelated?: boolean;
  maxResults?: number;
}

export class KnowledgeBaseService {
  private knowledgeBase: Map<string, KnowledgeNode> = new Map();
  private knowledgeIndex: KnowledgeIndex;
  private readonly KB_DIR = path.join(process.cwd(), 'knowledge_base');
  private readonly INDEX_FILE = path.join(this.KB_DIR, 'index.json');
  private readonly NODES_FILE = path.join(this.KB_DIR, 'nodes.json');
  private readonly STATS_FILE = path.join(this.KB_DIR, 'stats.json');
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时缓存

  constructor() {
    this.knowledgeIndex = {
      concepts: new Map(),
      categories: new Map(),
      tags: new Map(),
      lastUpdated: new Date()
    };
    
    this.initializeKnowledgeBase();
  }

  /**
   * 搜索知识库
   */
  async searchKnowledgeBase(
    query: string,
    options: KnowledgeSearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`📖 搜索知识库: ${query}`);

      // 生成缓存键
      const cacheKey = `kb_search_${this.hashString(query + JSON.stringify(options))}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as KnowledgeNode[] | null;
        if (cached) {
          console.log('✅ 从缓存获取知识库搜索结果');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      // 执行搜索
      const results = await this.performSearch(query, options);

      // 缓存结果
      if (options.useCache !== false && results.length > 0) {
        await defaultCache.set(cacheKey, results, this.CACHE_TTL);
      }

      console.log(`✅ 知识库搜索完成: 找到 ${results.length} 个节点`);

      return {
        success: true,
        data: results,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: results.length,
          sources: ['knowledge_base'],
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'searchKnowledgeBase', { query, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 添加知识节点
   */
  async addKnowledgeNode(node: KnowledgeNode): Promise<void> {
    try {
      // 添加到内存
      this.knowledgeBase.set(node.id, node);
      
      // 更新索引
      this.updateIndex(node);
      
      // 持久化保存
      await this.saveKnowledgeBase();
      
      console.log(`📝 添加知识节点: ${node.title}`);
      
    } catch (error) {
      console.error('❌ 添加知识节点失败:', error);
      throw error;
    }
  }

  /**
   * 批量添加知识节点
   */
  async addKnowledgeNodes(nodes: KnowledgeNode[]): Promise<void> {
    try {
      console.log(`📚 批量添加知识节点: ${nodes.length} 个`);
      
      for (const node of nodes) {
        this.knowledgeBase.set(node.id, node);
        this.updateIndex(node);
      }
      
      // 批量保存
      await this.saveKnowledgeBase();
      
      console.log('✅ 批量添加完成');
      
    } catch (error) {
      console.error('❌ 批量添加知识节点失败:', error);
      throw error;
    }
  }

  /**
   * 更新知识节点
   */
  async updateKnowledgeNode(nodeId: string, updates: Partial<KnowledgeNode>): Promise<void> {
    const existingNode = this.knowledgeBase.get(nodeId);
    if (!existingNode) {
      throw new Error(`知识节点不存在: ${nodeId}`);
    }

    // 更新节点
    const updatedNode: KnowledgeNode = {
      ...existingNode,
      ...updates,
      lastUpdated: new Date()
    };

    this.knowledgeBase.set(nodeId, updatedNode);
    this.updateIndex(updatedNode);
    
    await this.saveKnowledgeBase();
    
    console.log(`📝 更新知识节点: ${updatedNode.title}`);
  }

  /**
   * 删除知识节点
   */
  async deleteKnowledgeNode(nodeId: string): Promise<void> {
    const node = this.knowledgeBase.get(nodeId);
    if (!node) {
      throw new Error(`知识节点不存在: ${nodeId}`);
    }

    // 删除节点
    this.knowledgeBase.delete(nodeId);
    
    // 清理索引
    this.cleanupIndex(node);
    
    // 清理相关关系
    for (const [id, otherNode] of this.knowledgeBase.entries()) {
      otherNode.relationships = otherNode.relationships.filter(rel => rel.targetId !== nodeId);
    }
    
    await this.saveKnowledgeBase();
    
    console.log(`🗑️ 删除知识节点: ${node.title}`);
  }

  /**
   * 获取知识库统计信息
   */
  getKnowledgeStats(): KnowledgeStats {
    const nodes = Array.from(this.knowledgeBase.values());
    
    const categoryCounts: Record<string, number> = {};
    const typeCounts: Record<string, number> = {};
    let totalRelationships = 0;
    let totalImportance = 0;
    
    const coverage = {
      api: 0,
      examples: 0,
      tutorials: 0,
      concepts: 0
    };

    for (const node of nodes) {
      // 统计类别
      categoryCounts[node.category] = (categoryCounts[node.category] || 0) + 1;
      
      // 统计类型
      typeCounts[node.type] = (typeCounts[node.type] || 0) + 1;
      
      // 统计关系
      totalRelationships += node.relationships.length;
      
      // 统计重要性
      totalImportance += node.importance;
      
      // 统计覆盖率
      switch (node.type) {
        case 'api':
          coverage.api++;
          break;
        case 'example':
          coverage.examples++;
          break;
        case 'tutorial':
          coverage.tutorials++;
          break;
        case 'concept':
          coverage.concepts++;
          break;
      }
    }

    return {
      totalNodes: nodes.length,
      totalRelationships,
      categoryCounts,
      typeCounts,
      averageImportance: nodes.length > 0 ? totalImportance / nodes.length : 0,
      lastIndexed: this.knowledgeIndex.lastUpdated,
      coverage
    };
  }

  /**
   * 根据类别获取节点
   */
  getNodesByCategory(category: string): KnowledgeNode[] {
    const nodeIds = this.knowledgeIndex.categories.get(category) || [];
    return nodeIds.map(id => this.knowledgeBase.get(id)).filter(Boolean) as KnowledgeNode[];
  }

  /**
   * 根据标签获取节点
   */
  getNodesByTag(tag: string): KnowledgeNode[] {
    const nodeIds = this.knowledgeIndex.tags.get(tag) || [];
    return nodeIds.map(id => this.knowledgeBase.get(id)).filter(Boolean) as KnowledgeNode[];
  }

  /**
   * 获取节点的相关节点
   */
  getRelatedNodes(nodeId: string, maxResults: number = 10): KnowledgeNode[] {
    const node = this.knowledgeBase.get(nodeId);
    if (!node) return [];

    const related: Array<{ node: KnowledgeNode; strength: number }> = [];

    for (const relationship of node.relationships) {
      const relatedNode = this.knowledgeBase.get(relationship.targetId);
      if (relatedNode) {
        related.push({
          node: relatedNode,
          strength: relationship.strength
        });
      }
    }

    return related
      .sort((a, b) => b.strength - a.strength)
      .slice(0, maxResults)
      .map(item => item.node);
  }

  /**
   * 导出知识库
   */
  async exportKnowledgeBase(format: 'json' | 'csv' | 'xml' = 'json'): Promise<string> {
    const stats = this.getKnowledgeStats();
    const nodes = Array.from(this.knowledgeBase.values());
    
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0',
        stats
      },
      nodes
    };

    switch (format) {
      case 'json':
        return JSON.stringify(exportData, null, 2);
      
      case 'csv':
        return this.convertToCSV(nodes);
      
      case 'xml':
        return this.convertToXML(exportData);
      
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 导入知识库
   */
  async importKnowledgeBase(data: string, format: 'json' = 'json'): Promise<void> {
    try {
      let nodes: KnowledgeNode[] = [];

      switch (format) {
        case 'json':
          const parsed = JSON.parse(data);
          nodes = parsed.nodes || parsed; // 支持直接导入节点数组
          break;
        
        default:
          throw new Error(`不支持的导入格式: ${format}`);
      }

      // 验证节点数据
      for (const node of nodes) {
        this.validateNode(node);
      }

      // 批量添加
      await this.addKnowledgeNodes(nodes);
      
      console.log(`📥 导入知识库完成: ${nodes.length} 个节点`);
      
    } catch (error) {
      console.error('❌ 导入知识库失败:', error);
      throw error;
    }
  }

  /**
   * 重建索引
   */
  async rebuildIndex(): Promise<void> {
    console.log('🔄 重建知识库索引...');
    
    // 清空现有索引
    this.knowledgeIndex = {
      concepts: new Map(),
      categories: new Map(),
      tags: new Map(),
      lastUpdated: new Date()
    };

    // 重建索引
    for (const node of this.knowledgeBase.values()) {
      this.updateIndex(node);
    }

    await this.saveIndex();
    
    console.log('✅ 知识库索引重建完成');
  }

  /**
   * 清理无效关系
   */
  async cleanupInvalidRelationships(): Promise<number> {
    let cleanedCount = 0;
    
    for (const node of this.knowledgeBase.values()) {
      const validRelationships = node.relationships.filter(rel => 
        this.knowledgeBase.has(rel.targetId)
      );
      
      if (validRelationships.length !== node.relationships.length) {
        node.relationships = validRelationships;
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      await this.saveKnowledgeBase();
      console.log(`🧹 清理了 ${cleanedCount} 个节点的无效关系`);
    }
    
    return cleanedCount;
  }

  /**
   * 初始化知识库
   */
  private async initializeKnowledgeBase(): Promise<void> {
    try {
      // 确保知识库目录存在
      await fs.ensureDir(this.KB_DIR);
      
      // 加载现有知识库
      await this.loadKnowledgeBase();
      
      console.log(`📚 知识库初始化完成: ${this.knowledgeBase.size} 个节点`);
      
    } catch (error) {
      console.error('❌ 知识库初始化失败:', error);
      // 创建空的知识库
      await this.createEmptyKnowledgeBase();
    }
  }

  /**
   * 加载知识库
   */
  private async loadKnowledgeBase(): Promise<void> {
    // 加载节点
    if (await fs.pathExists(this.NODES_FILE)) {
      const nodesData = await fs.readJson(this.NODES_FILE);
      for (const nodeData of nodesData) {
        this.knowledgeBase.set(nodeData.id, nodeData);
      }
    }

    // 加载索引
    if (await fs.pathExists(this.INDEX_FILE)) {
      const indexData = await fs.readJson(this.INDEX_FILE);
      this.knowledgeIndex = {
        concepts: new Map(indexData.concepts || []),
        categories: new Map(indexData.categories || []),
        tags: new Map(indexData.tags || []),
        lastUpdated: new Date(indexData.lastUpdated || Date.now())
      };
    }
  }

  /**
   * 保存知识库
   */
  private async saveKnowledgeBase(): Promise<void> {
    try {
      // 保存节点
      const nodes = Array.from(this.knowledgeBase.values());
      await fs.writeJson(this.NODES_FILE, nodes, { spaces: 2 });
      
      // 保存索引
      await this.saveIndex();
      
      // 保存统计信息
      const stats = this.getKnowledgeStats();
      await fs.writeJson(this.STATS_FILE, stats, { spaces: 2 });
      
    } catch (error) {
      console.error('❌ 保存知识库失败:', error);
      throw error;
    }
  }

  /**
   * 保存索引
   */
  private async saveIndex(): Promise<void> {
    const indexData = {
      concepts: Array.from(this.knowledgeIndex.concepts.entries()),
      categories: Array.from(this.knowledgeIndex.categories.entries()),
      tags: Array.from(this.knowledgeIndex.tags.entries()),
      lastUpdated: this.knowledgeIndex.lastUpdated.toISOString()
    };
    
    await fs.writeJson(this.INDEX_FILE, indexData, { spaces: 2 });
  }

  /**
   * 创建空知识库
   */
  private async createEmptyKnowledgeBase(): Promise<void> {
    await fs.writeJson(this.NODES_FILE, [], { spaces: 2 });
    await this.saveIndex();
    console.log('📝 创建了空的知识库');
  }

  /**
   * 执行搜索
   */
  private async performSearch(query: string, options: KnowledgeSearchOptions): Promise<KnowledgeNode[]> {
    const queryLower = query.toLowerCase();
    const results: Array<{ node: KnowledgeNode; score: number }> = [];

    for (const node of this.knowledgeBase.values()) {
      // 应用过滤器
      if (options.category && node.category !== options.category) continue;
      if (options.nodeType && node.type !== options.nodeType) continue;
      if (options.minImportance && node.importance < options.minImportance) continue;

      // 计算相关性得分
      let score = 0;

      // 标题匹配
      if (node.title.toLowerCase().includes(queryLower)) {
        score += 3;
      }

      // 内容匹配
      if (node.content.toLowerCase().includes(queryLower)) {
        score += 2;
      }

      // 标签匹配
      for (const tag of node.tags) {
        if (tag.toLowerCase().includes(queryLower)) {
          score += 1;
        }
      }

      // 重要性加权
      score *= node.importance;

      if (score > 0) {
        results.push({ node, score });
      }
    }

    // 排序并返回结果
    const sortedResults = results
      .sort((a, b) => b.score - a.score)
      .slice(0, options.maxResults || 20)
      .map(item => item.node);

    // 包含相关节点
    if (options.includeRelated && sortedResults.length > 0) {
      const relatedNodes = new Set<KnowledgeNode>();
      
      for (const node of sortedResults.slice(0, 3)) { // 只对前3个结果查找相关
        const related = this.getRelatedNodes(node.id, 3);
        related.forEach(rNode => relatedNodes.add(rNode));
      }
      
      // 添加相关节点但不超过总数限制
      const finalResults = [...sortedResults];
      for (const relatedNode of relatedNodes) {
        if (!finalResults.includes(relatedNode) && finalResults.length < (options.maxResults || 20)) {
          finalResults.push(relatedNode);
        }
      }
      
      return finalResults;
    }

    return sortedResults;
  }

  /**
   * 更新索引
   */
  private updateIndex(node: KnowledgeNode): void {
    // 更新类别索引
    if (!this.knowledgeIndex.categories.has(node.category)) {
      this.knowledgeIndex.categories.set(node.category, []);
    }
    const categoryNodes = this.knowledgeIndex.categories.get(node.category)!;
    if (!categoryNodes.includes(node.id)) {
      categoryNodes.push(node.id);
    }

    // 更新标签索引
    for (const tag of node.tags) {
      if (!this.knowledgeIndex.tags.has(tag)) {
        this.knowledgeIndex.tags.set(tag, []);
      }
      const tagNodes = this.knowledgeIndex.tags.get(tag)!;
      if (!tagNodes.includes(node.id)) {
        tagNodes.push(node.id);
      }
    }

    // 更新概念索引（基于标题中的关键词）
    const titleWords = node.title.toLowerCase().split(/\s+/);
    for (const word of titleWords) {
      if (word.length > 2) { // 只索引长度大于2的词
        if (!this.knowledgeIndex.concepts.has(word)) {
          this.knowledgeIndex.concepts.set(word, []);
        }
        const conceptNodes = this.knowledgeIndex.concepts.get(word)!;
        if (!conceptNodes.includes(node.id)) {
          conceptNodes.push(node.id);
        }
      }
    }

    this.knowledgeIndex.lastUpdated = new Date();
  }

  /**
   * 清理索引
   */
  private cleanupIndex(node: KnowledgeNode): void {
    // 清理类别索引
    const categoryNodes = this.knowledgeIndex.categories.get(node.category);
    if (categoryNodes) {
      const index = categoryNodes.indexOf(node.id);
      if (index > -1) {
        categoryNodes.splice(index, 1);
      }
      if (categoryNodes.length === 0) {
        this.knowledgeIndex.categories.delete(node.category);
      }
    }

    // 清理标签索引
    for (const tag of node.tags) {
      const tagNodes = this.knowledgeIndex.tags.get(tag);
      if (tagNodes) {
        const index = tagNodes.indexOf(node.id);
        if (index > -1) {
          tagNodes.splice(index, 1);
        }
        if (tagNodes.length === 0) {
          this.knowledgeIndex.tags.delete(tag);
        }
      }
    }

    // 清理概念索引
    for (const [concept, nodeIds] of this.knowledgeIndex.concepts.entries()) {
      const index = nodeIds.indexOf(node.id);
      if (index > -1) {
        nodeIds.splice(index, 1);
        if (nodeIds.length === 0) {
          this.knowledgeIndex.concepts.delete(concept);
        }
      }
    }

    this.knowledgeIndex.lastUpdated = new Date();
  }

  /**
   * 转换为CSV格式
   */
  private convertToCSV(nodes: KnowledgeNode[]): string {
    const headers = ['id', 'title', 'content', 'type', 'category', 'tags', 'importance', 'lastUpdated'];
    const rows = [headers.join(',')];

    for (const node of nodes) {
      const row = [
        node.id,
        `"${node.title.replace(/"/g, '""')}"`,
        `"${node.content.replace(/"/g, '""')}"`,
        node.type,
        node.category,
        `"${node.tags.join(';')}"`,
        node.importance.toString(),
        node.lastUpdated.toISOString()
      ];
      rows.push(row.join(','));
    }

    return rows.join('\n');
  }

  /**
   * 转换为XML格式
   */
  private convertToXML(data: any): string {
    const escapeXml = (text: string) => 
      text.replace(/[<>&'"]/g, char => {
        const map: Record<string, string> = {
          '<': '&lt;',
          '>': '&gt;',
          '&': '&amp;',
          "'": '&apos;',
          '"': '&quot;'
        };
        return map[char];
      });

    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<knowledgeBase>\n';
    xml += `  <metadata>\n`;
    xml += `    <exportDate>${data.metadata.exportDate}</exportDate>\n`;
    xml += `    <version>${data.metadata.version}</version>\n`;
    xml += `  </metadata>\n`;
    xml += '  <nodes>\n';

    for (const node of data.nodes) {
      xml += '    <node>\n';
      xml += `      <id>${escapeXml(node.id)}</id>\n`;
      xml += `      <title>${escapeXml(node.title)}</title>\n`;
      xml += `      <content>${escapeXml(node.content)}</content>\n`;
      xml += `      <type>${node.type}</type>\n`;
      xml += `      <category>${node.category}</category>\n`;
      xml += `      <importance>${node.importance}</importance>\n`;
      xml += `      <lastUpdated>${node.lastUpdated}</lastUpdated>\n`;
      xml += '      <tags>\n';
      for (const tag of node.tags) {
        xml += `        <tag>${escapeXml(tag)}</tag>\n`;
      }
      xml += '      </tags>\n';
      xml += '    </node>\n';
    }

    xml += '  </nodes>\n';
    xml += '</knowledgeBase>';

    return xml;
  }

  /**
   * 验证节点数据
   */
  private validateNode(node: any): void {
    const required = ['id', 'title', 'content', 'type', 'category'];
    
    for (const field of required) {
      if (!node[field]) {
        throw new Error(`节点缺少必需字段: ${field}`);
      }
    }

    if (!['concept', 'api', 'example', 'tutorial', 'best_practice'].includes(node.type)) {
      throw new Error(`无效的节点类型: ${node.type}`);
    }
  }

  /**
   * 字符串哈希
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }
}

// 导出知识库服务实例
export const knowledgeBaseService = new KnowledgeBaseService();

// 导出类型
export type {
  KnowledgeIndex,
  KnowledgeStats,
  KnowledgeSearchOptions
};