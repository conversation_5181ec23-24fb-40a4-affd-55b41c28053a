/**
 * 迁移指南服务
 * 负责提供从 Cocos2d-x 到 Axmol 的迁移指南和帮助
 */

import { MigrationGuide, MigrationChange, CodeExample, BuildIssue, AxmolResource, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

export class MigrationService {
  private readonly GITHUB_RAW_BASE = 'https://raw.githubusercontent.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 4 * 60 * 60 * 1000; // 4小时缓存

  // 迁移指南知识库
  private readonly migrationKnowledge: Map<string, MigrationGuide> = new Map();

  constructor() {
    this.initializeMigrationKnowledge();
  }

  /**
   * 获取迁移指南
   */
  async getMigrationGuide(
    fromEngine: string,
    topic: string = 'general',
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 获取迁移指南: ${fromEngine} -> Axmol (主题: ${topic})`);

      // 生成缓存键
      const cacheKey = `migration_${fromEngine}_${topic}_${JSON.stringify(options)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as MigrationGuide | null;
        if (cached) {
          console.log('✅ 从缓存获取迁移指南');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: 1,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];
      let migrationGuide: MigrationGuide | null = null;

      // 1. 检查内置知识库
      migrationGuide = this.getBuiltinMigrationGuide(fromEngine, topic);
      if (migrationGuide) {
        sources.push('builtin_knowledge');
      }

      // 2. 搜索官方迁移文档
      if (!migrationGuide) {
        migrationGuide = await this.searchOfficialMigrationDocs(fromEngine, topic);
        if (migrationGuide) {
          sources.push('official_docs');
        }
      }

      // 3. 搜索社区迁移经验
      if (!migrationGuide) {
        migrationGuide = await this.searchCommunityMigrationGuides(fromEngine, topic);
        if (migrationGuide) {
          sources.push('community');
        }
      }

      // 4. 生成基础迁移指南
      if (!migrationGuide) {
        migrationGuide = this.generateBasicMigrationGuide(fromEngine, topic);
        sources.push('generated');
      }

      // 增强迁移指南
      if (migrationGuide) {
        migrationGuide = await this.enhanceMigrationGuide(migrationGuide);
      }

      if (!migrationGuide) {
        throw new Error(`未找到从 ${fromEngine} 到 Axmol 的迁移指南`);
      }

      // 缓存结果
      if (options.useCache !== false) {
        await defaultCache.set(cacheKey, migrationGuide, this.CACHE_TTL);
      }

      console.log(`✅ 迁移指南获取完成: ${fromEngine} -> Axmol`);

      return {
        success: true,
        data: migrationGuide,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 1,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'getMigrationGuide', { fromEngine, topic, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 初始化迁移知识库
   */
  private initializeMigrationKnowledge(): void {
    // Cocos2d-x 3.x 到 Axmol 的通用迁移指南
    this.migrationKnowledge.set('cocos2d-x_3.x_general', {
      fromEngine: 'Cocos2d-x 3.x',
      toEngine: 'Axmol',
      topic: 'general',
      changes: [
        {
          category: '命名空间',
          oldApi: 'cocos2d::',
          newApi: 'ax::',
          description: '所有Cocos2d-x的命名空间都改为ax',
          breaking: true,
          migrationSteps: [
            '全局替换 "cocos2d::" 为 "ax::"',
            '更新所有头文件包含路径',
            '检查自定义类的继承关系'
          ]
        },
        {
          category: '头文件',
          oldApi: '#include "cocos2d.h"',
          newApi: '#include "axmol.h"',
          description: '主头文件名称变更',
          breaking: true,
          migrationSteps: [
            '替换主头文件包含',
            '更新其他相关头文件路径',
            '检查预编译头文件配置'
          ]
        },
        {
          category: 'Director',
          oldApi: 'Director::getInstance()',
          newApi: 'Director::getInstance()',
          description: 'Director API保持兼容，但命名空间变更',
          breaking: false,
          migrationSteps: [
            '更新命名空间引用',
            '检查Director相关的自定义代码'
          ]
        }
      ],
      examples: [
        {
          title: '基础场景创建迁移示例',
          language: 'cpp',
          code: `// Cocos2d-x 3.x
#include "cocos2d.h"
USING_NS_CC;

class HelloWorld : public cocos2d::Scene
{
public:
    static cocos2d::Scene* createScene();
    virtual bool init();
    CREATE_FUNC(HelloWorld);
};

// Axmol
#include "axmol.h"
USING_NS_AX;

class HelloWorld : public ax::Scene
{
public:
    static ax::Scene* createScene();
    virtual bool init();
    CREATE_FUNC(HelloWorld);
};`,
          description: '展示基础的命名空间和头文件迁移'
        }
      ],
      commonIssues: [],
      resources: []
    });

    // Cocos2d-x 4.x 到 Axmol 的迁移指南
    this.migrationKnowledge.set('cocos2d-x_4.x_general', {
      fromEngine: 'Cocos2d-x 4.x',
      toEngine: 'Axmol',
      topic: 'general',
      changes: [
        {
          category: '渲染系统',
          oldApi: 'Renderer',
          newApi: 'Renderer (Enhanced)',
          description: 'Axmol增强了渲染系统性能',
          breaking: false,
          migrationSteps: [
            '检查自定义渲染代码',
            '利用新的渲染优化特性',
            '更新着色器代码（如有）'
          ]
        },
        {
          category: '音频系统',
          oldApi: 'AudioEngine',
          newApi: 'AudioEngine (Improved)',
          description: '音频系统得到改进和优化',
          breaking: false,
          migrationSteps: [
            '测试现有音频功能',
            '利用新的音频特性',
            '检查音频文件格式兼容性'
          ]
        }
      ],
      examples: [],
      commonIssues: [],
      resources: []
    });

    console.log(`📚 初始化了 ${this.migrationKnowledge.size} 个迁移指南`);
  }

  /**
   * 获取内置迁移指南
   */
  private getBuiltinMigrationGuide(fromEngine: string, topic: string): MigrationGuide | null {
    const key = `${fromEngine.toLowerCase()}_${topic}`;
    
    // 尝试精确匹配
    let guide = this.migrationKnowledge.get(key);
    if (guide) return guide;

    // 尝试模糊匹配
    for (const [mapKey, mapGuide] of this.migrationKnowledge.entries()) {
      if (mapKey.includes(fromEngine.toLowerCase()) || mapKey.includes(topic.toLowerCase())) {
        return mapGuide;
      }
    }

    return null;
  }

  /**
   * 搜索官方迁移文档
   */
  private async searchOfficialMigrationDocs(fromEngine: string, topic: string): Promise<MigrationGuide | null> {
    try {
      // 减少搜索路径，只搜索最可能的文档
      const migrationPaths = ['docs/MIGRATION.md', 'MIGRATION.md'];

      for (const path of migrationPaths) {
        try {
          const fileUrl = `${this.GITHUB_RAW_BASE}/${this.AXMOL_REPO}/dev/${path}`;

          // 添加超时控制
          const response = await Promise.race([
            networkUtils.get(fileUrl, { timeout: 4000 }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Migration doc timeout')), 5000) // 减少超时时间
            )
          ]) as any;

          const guide = this.parseMigrationDocument(response.data, fromEngine, topic);
          if (guide) {
            console.log(`✅ 从官方文档获取迁移指南: ${path}`);
            return guide;
          }

        } catch (error) {
          console.log(`⚠️ 迁移文档 ${path} 获取超时或失败`);
          continue;
        }
      }

    } catch (error) {
      console.log('⚠️ 官方迁移文档搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 解析迁移文档
   */
  private parseMigrationDocument(content: string, fromEngine: string, topic: string): MigrationGuide | null {
    try {
      // 简化的文档解析逻辑
      const lines = content.split('\n');
      const changes: MigrationChange[] = [];
      const examples: CodeExample[] = [];

      let currentSection = '';
      let codeBlock = '';
      let inCodeBlock = false;

      for (const line of lines) {
        if (line.startsWith('# ') || line.startsWith('## ')) {
          currentSection = line.replace(/^#+\s*/, '');
        }

        if (line.startsWith('```')) {
          if (inCodeBlock) {
            // 结束代码块
            if (codeBlock.trim()) {
              examples.push({
                title: `${currentSection} 示例`,
                language: 'cpp',
                code: codeBlock.trim(),
                description: `从迁移文档提取的 ${currentSection} 示例`
              });
            }
            codeBlock = '';
            inCodeBlock = false;
          } else {
            // 开始代码块
            inCodeBlock = true;
          }
        } else if (inCodeBlock) {
          codeBlock += line + '\n';
        }

        // 查找API变更信息
        if (line.includes('->') || line.includes('changed to') || line.includes('replaced by')) {
          const change = this.parseChangeFromLine(line, currentSection);
          if (change) {
            changes.push(change);
          }
        }
      }

      if (changes.length > 0 || examples.length > 0) {
        return {
          fromEngine,
          toEngine: 'Axmol',
          topic,
          changes,
          examples,
          commonIssues: [],
          resources: []
        };
      }

    } catch (error) {
      console.log('⚠️ 迁移文档解析失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 从行中解析变更信息
   */
  private parseChangeFromLine(line: string, category: string): MigrationChange | null {
    try {
      // 简化的变更解析
      const patterns = [
        /`([^`]+)`\s*->\s*`([^`]+)`/,
        /`([^`]+)`\s+changed to\s+`([^`]+)`/,
        /`([^`]+)`\s+replaced by\s+`([^`]+)`/
      ];

      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          return {
            category,
            oldApi: match[1],
            newApi: match[2],
            description: line.trim(),
            breaking: line.toLowerCase().includes('breaking') || line.includes('!'),
            migrationSteps: [
              `将 ${match[1]} 替换为 ${match[2]}`,
              '测试相关功能',
              '检查是否有其他依赖需要更新'
            ]
          };
        }
      }

    } catch (error) {
      // 忽略解析错误
    }

    return null;
  }

  /**
   * 搜索社区迁移指南
   */
  private async searchCommunityMigrationGuides(fromEngine: string, topic: string): Promise<MigrationGuide | null> {
    // 这里可以实现搜索社区博客、论坛等的迁移经验
    // 目前返回null，后续可以扩展
    return null;
  }

  /**
   * 生成基础迁移指南
   */
  private generateBasicMigrationGuide(fromEngine: string, topic: string): MigrationGuide {
    const basicChanges: MigrationChange[] = [
      {
        category: '基础设置',
        oldApi: fromEngine,
        newApi: 'Axmol',
        description: `从 ${fromEngine} 迁移到 Axmol 的基础步骤`,
        breaking: true,
        migrationSteps: [
          '备份现有项目',
          '下载并安装 Axmol',
          '创建新的 Axmol 项目',
          '逐步迁移代码和资源',
          '测试所有功能'
        ]
      }
    ];

    const basicExamples: CodeExample[] = [
      {
        title: '基础项目结构',
        language: 'cpp',
        code: `// 基础的 Axmol 应用结构
#include "axmol.h"
USING_NS_AX;

class AppDelegate : public Application
{
public:
    virtual bool applicationDidFinishLaunching();
    virtual void applicationDidEnterBackground();
    virtual void applicationWillEnterForeground();
};`,
        description: 'Axmol 应用的基础结构示例'
      }
    ];

    return {
      fromEngine,
      toEngine: 'Axmol',
      topic,
      changes: basicChanges,
      examples: basicExamples,
      commonIssues: [],
      resources: [
        {
          type: 'official_docs',
          title: 'Axmol 官方文档',
          url: 'https://axmol.dev/manual/latest',
          content: '',
          relevanceScore: 10,
          matchedTerms: ['migration', 'guide'],
          source: 'official'
        }
      ]
    };
  }

  /**
   * 增强迁移指南
   */
  private async enhanceMigrationGuide(guide: MigrationGuide): Promise<MigrationGuide> {
    // 添加常见问题
    guide.commonIssues = await this.generateCommonMigrationIssues(guide.fromEngine);
    
    // 添加相关资源
    if (guide.resources.length === 0) {
      guide.resources = await this.findMigrationResources(guide.fromEngine, guide.topic);
    }

    return guide;
  }

  /**
   * 生成常见迁移问题
   */
  private async generateCommonMigrationIssues(fromEngine: string): Promise<BuildIssue[]> {
    const issues: BuildIssue[] = [
      {
        platform: 'all',
        errorMessage: '编译错误：找不到头文件',
        errorType: 'compile',
        solutions: [
          {
            title: '更新头文件路径',
            description: '迁移后需要更新所有头文件的包含路径',
            steps: [
              '检查所有 #include 语句',
              '将 Cocos2d-x 头文件路径替换为 Axmol 路径',
              '更新项目配置中的头文件搜索路径'
            ],
            codeChanges: [],
            configChanges: [],
            priority: 'high',
            verified: true
          }
        ],
        relatedIssues: []
      }
    ];

    return issues;
  }

  /**
   * 查找迁移资源
   */
  private async findMigrationResources(fromEngine: string, topic: string): Promise<AxmolResource[]> {
    const resources: AxmolResource[] = [
      {
        type: 'official_docs',
        title: 'Axmol GitHub 仓库',
        url: `https://github.com/${this.AXMOL_REPO}`,
        content: 'Axmol 引擎的官方 GitHub 仓库',
        relevanceScore: 10,
        matchedTerms: ['axmol', 'migration'],
        source: 'official'
      },
      {
        type: 'wiki',
        title: 'Axmol Wiki',
        url: `https://github.com/${this.AXMOL_REPO}/wiki`,
        content: 'Axmol 引擎的官方 Wiki 文档',
        relevanceScore: 9,
        matchedTerms: ['wiki', 'documentation'],
        source: 'official'
      }
    ];

    return resources;
  }
}

// 导出默认迁移服务实例
export const migrationService = new MigrationService();
