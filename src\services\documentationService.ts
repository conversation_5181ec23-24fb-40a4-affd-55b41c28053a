/**
 * 文档搜索服务
 * 负责搜索 Axmol 官方文档和技术资料
 */

import * as cheerio from 'cheerio';
import { AxmolResource, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';
import { ConcurrencyManager } from '../utils/concurrencyUtils.js';
import { cacheKey } from '../utils/cacheKeyUtils.js';

export class DocumentationService {
  private readonly OFFICIAL_DOCS_BASE = 'https://axmol.dev/manual/latest';
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30分钟缓存
  private readonly concurrencyManager = new ConcurrencyManager();
  
  // 增强的搜索关键词映射
  private readonly enhancedKeywordMap: { [key: string]: string[] } = {
    'sprite': ['sprite', 'spriteframe', 'spritebatchnode', 'texture2d', 'image', 'displayobject'],
    'animation': ['animation', 'animate', 'action', 'actioninterval', 'sequence', 'spawn', 'repeat'],
    'scene': ['scene', 'layer', 'node', 'director', 'transition', 'scenemanager'],
    'physics': ['physicsbody', 'physicsworld', 'physicsshape', 'physicsjoint', 'collision'],
    'audio': ['audio', 'audioengine', 'sound', 'music', 'simpleaudioengine'],
    'ui': ['ui', 'widget', 'button', 'label', 'textfield', 'layout', 'scrollview'],
    'render': ['renderer', 'rendercommand', 'renderstate', 'camera', 'light'],
    'input': ['touch', 'keyboard', 'mouse', 'eventlistener', 'eventdispatcher'],
    '3d': ['sprite3d', 'mesh', 'meshskin', 'animation3d', 'camera', 'light3d'],
    'network': ['httpclient', 'websocket', 'downloadfile'],
    'storage': ['userdefault', 'fileutils', 'data', 'database'],
    'math': ['vec2', 'vec3', 'vec4', 'mat4', 'quaternion', 'size', 'rect']
  };

  /**
   * 搜索 Axmol 文档
   */
  async searchDocumentation(
    query: string,
    sourceType: string = 'all',
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 开始搜索文档: "${query}" (类型: ${sourceType})`);

      // 生成缓存键
      const cacheKeyValue = cacheKey.docs(query, { sourceType, ...options });
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKeyValue) as AxmolResource[] | null;
        if (cached) {
          console.log('✅ 从缓存获取文档搜索结果');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const results: AxmolResource[] = [];
      const sources: string[] = [];

      // 根据源类型搜索不同的文档源
      if (sourceType === 'all' || sourceType === 'official') {
        const officialResults = await this.searchOfficialDocs(query, options);
        results.push(...officialResults);
        if (officialResults.length > 0) sources.push('official_docs');
      }

      if (sourceType === 'all' || sourceType === 'wiki') {
        const wikiResults = await this.searchWikiDocs(query, options);
        results.push(...wikiResults);
        if (wikiResults.length > 0) sources.push('wiki');
      }

      if (sourceType === 'all' || sourceType === 'api') {
        const apiResults = await this.searchApiDocs(query, options);
        results.push(...apiResults);
        if (apiResults.length > 0) sources.push('api_docs');
      }

      // 按相关性排序
      const sortedResults = this.sortByRelevance(results, query);
      
      // 限制结果数量
      const maxResults = options.maxResults || 20;
      const finalResults = sortedResults.slice(0, maxResults);

      // 缓存结果
      if (options.useCache !== false && finalResults.length > 0) {
        await defaultCache.set(cacheKeyValue, finalResults, this.CACHE_TTL);
      }

      console.log(`✅ 文档搜索完成: 找到 ${finalResults.length} 个结果`);

      return {
        success: true,
        data: finalResults,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: finalResults.length,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'searchDocumentation', { query, sourceType, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 搜索官方文档
   */
  private async searchOfficialDocs(query: string, options: SearchOptions): Promise<AxmolResource[]> {
    const results: AxmolResource[] = [];

    try {
      // 搜索主页内容
      const mainPageResult = await this.searchOfficialMainPage(query);
      if (mainPageResult) {
        results.push(mainPageResult);
      }

      // 搜索API页面 - 优化并发和超时控制
      const apiPages = await this.discoverApiPages(query);
      const limitedPages = apiPages.slice(0, 2); // 进一步减少到2个页面

      // 使用并发管理器优化API页面搜索
      const searchTasks = limitedPages.map(page => 
        () => this.searchApiPage(page, query)
      );

      const pageResults = await ConcurrencyManager.executeConcurrent(searchTasks, {
        maxConcurrent: 2,
        timeout: 3000,
        retries: 1
      });

      results.push(...pageResults.filter(result => result !== null));

      console.log(`📚 官方文档搜索完成: ${results.length} 个结果`);

    } catch (error) {
      console.log('⚠️ 官方文档搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return results;
  }

  /**
   * 搜索官方主页
   */
  private async searchOfficialMainPage(query: string): Promise<AxmolResource | null> {
    try {
      console.log(`🌐 正在请求官方主页: ${this.OFFICIAL_DOCS_BASE}`);
      const response = await networkUtils.get(this.OFFICIAL_DOCS_BASE, {
        timeout: 10000
      });

      console.log(`✅ 网络请求成功，状态码: ${response.status}`);
      console.log(`📄 响应数据长度: ${response.data?.length || 0} 字符`);

      const $ = cheerio.load(response.data);
      const title = 'Axmol Engine 官方API文档';
      const content = $('body').text();

      console.log(`📄 解析后内容长度: ${content.length} 字符`);
      console.log(`📄 内容预览: "${content.substring(0, 300)}..."`);

      // 检查相关性
      const relevanceScore = this.calculateRelevance(content, query);
      console.log(`🔍 官方主页相关性分数: ${relevanceScore} (查询: "${query}")`);

      if (relevanceScore > 0) {
        return {
          type: 'official_docs',
          title,
          url: this.OFFICIAL_DOCS_BASE,
          content: content.substring(0, 2000),
          relevanceScore,
          matchedTerms: this.extractMatchedTerms(content, query),
          source: 'official_docs',
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      console.log('⚠️ 获取官方主页失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 发现相关的API页面
   */
  private async discoverApiPages(query: string): Promise<string[]> {
    const pages: string[] = [];
    
    // 基于查询词生成可能的API页面路径
    const queryTerms = query.toLowerCase().split(/\s+/);
    const classNameMappings: { [key: string]: string[] } = {
      'sprite': ['sprite', 'spritebatchnode', 'spriteframe'],
      'node': ['node'],
      'scene': ['scene', 'scenemgr'],
      'director': ['director'],
      'action': ['action', 'actionmanager', 'actionease'],
      'animation': ['animation', 'animate'],
      'event': ['event', 'eventlistener', 'eventdispatcher'],
      'touch': ['touch', 'eventtouch'],
      'keyboard': ['keyboard', 'eventkeyboard'],
      'mouse': ['mouse', 'eventmouse'],
      'physics': ['physicsbody', 'physicsworld', 'physicsshape'],
      'audio': ['audio', 'audioengine'],
      'ui': ['widget', 'button', 'label', 'textfield'],
      'render': ['renderer', 'rendercommand'],
      'texture': ['texture2d', 'textureatlasnode'],
      'camera': ['camera'],
      'light': ['light', 'ambientlight', 'directionallight'],
      'mesh': ['mesh', 'meshmaterial', 'meshvertexdata'],
      '3d': ['sprite3d', 'mesh', 'camera', 'light']
    };

    // 为每个查询词查找对应的类名
    queryTerms.forEach(term => {
      // 直接匹配
      if (classNameMappings[term]) {
        classNameMappings[term].forEach(className => {
          pages.push(`classax_1_1_${className}.html`);
        });
      }

      // 部分匹配
      Object.entries(classNameMappings).forEach(([key, classNames]) => {
        if (key.includes(term) || term.includes(key)) {
          classNames.forEach(className => {
            pages.push(`classax_1_1_${className}.html`);
          });
        }
      });
    });

    return [...new Set(pages)]; // 去重
  }

  /**
   * 搜索特定API页面
   */
  private async searchApiPage(
    page: string, 
    query: string, 
    analysis?: ReturnType<typeof this.analyzeQuery>
  ): Promise<AxmolResource | null> {
    try {
      const pageUrl = `${this.OFFICIAL_DOCS_BASE}/${page}`;
      
      const response = await networkUtils.get(pageUrl, {
        timeout: 8000,
        validateStatus: (status) => status === 200
      });

      const $ = cheerio.load(response.data);
      const title = $('title').text() || $('h1').first().text() || page;
      const content = $('body').text();

      // 检查页面是否有效
      if (content.includes('Page not found') || content.includes('404') || content.length < 100) {
        return null;
      }

      // 增强的相关性检查
      const relevanceScore = analysis ? 
        this.calculateEnhancedRelevance(content, query, analysis) :
        this.calculateRelevance(content, query);
      
      if (relevanceScore > 0) {
        return {
          type: 'official_docs',
          title,
          url: pageUrl,
          content: content.substring(0, 2000),
          relevanceScore,
          matchedTerms: analysis ? 
            this.extractEnhancedMatchedTerms(content, query, analysis) :
            this.extractMatchedTerms(content, query),
          source: 'api_page',
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      // 忽略404等常见错误
      if (!(error instanceof Error) || !error.message?.includes('404')) {
        console.log(`⚠️ API页面搜索失败 ${page}:`, error instanceof Error ? error.message : String(error));
      }
    }

    return null;
  }

  /**
   * 搜索Wiki文档
   */
  private async searchWikiDocs(query: string, options: SearchOptions): Promise<AxmolResource[]> {
    const results: AxmolResource[] = [];

    // 减少Wiki页面数量，优先搜索相关页面
    const queryLower = query.toLowerCase();
    const allWikiPages = [
      'Getting-Started', 'Sprite', 'Animation', 'Physics', 'Actions',
      'Scene-Management', 'UI-System', 'Audio', 'Input-Handling',
      'Platform-Specific', 'Performance', 'Troubleshooting'
    ];

    // 根据查询内容选择相关页面
    const relevantPages = allWikiPages.filter(page =>
      page.toLowerCase().includes(queryLower) ||
      queryLower.includes(page.toLowerCase().replace('-', ''))
    );

    const wikiPages = relevantPages.length > 0 ? relevantPages.slice(0, 3) : allWikiPages.slice(0, 3);

    try {
      const wikiBaseUrl = dataSourceManager.buildUrl('github_axmol_wiki', '');

      for (const page of wikiPages) {
        try {
          const pageUrl = `${wikiBaseUrl}/${page}`;

          // 添加超时控制
          const response = await Promise.race([
            networkUtils.get(pageUrl, {
              timeout: 5000,
              headers: networkUtils.getWikiHeaders()
            }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Wiki page timeout')), 6000)
            )
          ]) as any;

          const $ = cheerio.load(response.data);
          const content = $('article, .markdown-body, .wiki-body').text().trim();
          const title = $('h1, .page-title').first().text().trim() || page;

          const relevanceScore = this.calculateRelevance(content, query);

          if (relevanceScore > 0) {
            results.push({
              type: 'wiki',
              title,
              url: pageUrl,
              content: content.substring(0, 3000),
              relevanceScore,
              matchedTerms: this.extractMatchedTerms(content, query),
              source: 'wiki',
              timestamp: new Date().toISOString()
            });
          }

        } catch (error) {
          console.log(`⚠️ Wiki页面 ${page} 搜索超时或失败`);
          continue;
        }
      }

      console.log(`📖 Wiki文档搜索完成: ${results.length} 个结果`);

    } catch (error) {
      console.log('⚠️ Wiki文档搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 搜索API文档
   */
  private async searchApiDocs(query: string, options: SearchOptions): Promise<AxmolResource[]> {
    // 这里可以实现更专门的API文档搜索逻辑
    // 目前复用官方文档搜索
    return this.searchOfficialDocs(query, options);
  }

  /**
   * 计算内容与查询的相关性分数
   */
  private calculateRelevance(content: string, query: string): number {
    const contentLower = content.toLowerCase();
    const queryTerms = this.extractSearchTerms(query);
    let score = 0;

    queryTerms.forEach(term => {
      if (term.length < 2) return; // 忽略太短的词

      let termScore = 0;

      // 精确匹配 - 提高权重
      const exactMatches = contentLower.match(new RegExp(term, 'gi'));
      if (exactMatches) {
        termScore += exactMatches.length * 5; // 提高精确匹配权重
      }

      // 部分匹配
      const partialTerm = term.substring(0, Math.max(3, term.length - 2));
      const partialMatches = contentLower.match(new RegExp(partialTerm, 'gi'));
      if (partialMatches && !exactMatches) {
        termScore += partialMatches.length * 2; // 提高部分匹配权重
      }

      // 游戏引擎相关术语的特殊处理
      const gameEngineScore = this.calculateGameEngineRelevance(contentLower, term);
      if (gameEngineScore > 0) {
        termScore += gameEngineScore * 2; // 提高相关术语权重
      }

      // 精灵动画相关的特殊处理
      const spriteAnimationScore = this.calculateSpriteAnimationRelevance(contentLower, term);
      if (spriteAnimationScore > 0) {
        termScore += spriteAnimationScore * 3; // 精灵动画相关术语高权重
      }

      score += termScore;
    });

    // 如果是游戏引擎相关的查询但没有直接匹配，给予基础分数
    if (score === 0 && this.isGameEngineRelated(query)) {
      score = 3; // 提高基础相关性分数
    }

    // 精灵动画相关查询的特殊处理
    if (this.isSpriteAnimationRelated(query)) {
      score += 5; // 为精灵动画相关查询增加基础分数
    }

    return score;
  }

  /**
   * 提取搜索词汇（支持中英文混合）
   */
  private extractSearchTerms(query: string): string[] {
    const terms: string[] = [];

    // 按空格分割
    const spaceSplit = query.toLowerCase().split(/\s+/);
    terms.push(...spaceSplit);

    // 中文词汇处理：如果包含中文，尝试提取英文部分
    const englishWords = query.match(/[a-zA-Z]+/g);
    if (englishWords) {
      terms.push(...englishWords.map(w => w.toLowerCase()));
    }

    // 去重并过滤
    return [...new Set(terms)].filter(term => term.length >= 2);
  }

  /**
   * 计算游戏引擎相关术语的相关性
   */
  private calculateGameEngineRelevance(content: string, term: string): number {
    const gameEngineTerms: Record<string, string[]> = {
      'sprite': ['node', 'texture', 'image', 'render', '2d', 'game', 'engine', 'spriteframe', 'spritebatchnode'],
      'animation': ['action', 'tween', 'frame', 'sequence', 'game', 'animate', 'animationframe'],
      'scene': ['layer', 'director', 'transition', 'game'],
      'texture': ['image', 'sprite', 'render', 'gl', '2d', 'spriteframe'],
      'sound': ['audio', 'music', 'effect', 'game'],
      'physics': ['body', 'collision', 'world', 'game'],
      'ui': ['button', 'label', 'widget', 'interface', 'game'],
      'camera': ['view', 'projection', '3d', 'game'],
      'light': ['render', '3d', 'shadow', 'game'],
      'mesh': ['3d', 'vertex', 'model', 'game'],
      'sheet': ['sprite', 'texture', 'atlas', 'frame', 'plist'],
      'frame': ['sprite', 'animation', 'texture', 'sequence'],
      '精灵': ['sprite', 'node', 'texture', 'image'],
      '动画': ['animation', 'animate', 'action', 'frame'],
      '图集': ['sheet', 'atlas', 'texture', 'sprite']
    };

    if (gameEngineTerms[term]) {
      let relatedScore = 0;
      gameEngineTerms[term].forEach((relatedTerm: string) => {
        if (content.includes(relatedTerm)) {
          relatedScore += 1.5; // 提高相关术语的权重
        }
      });
      return relatedScore;
    }

    return 0;
  }

  /**
   * 计算精灵动画相关术语的相关性
   */
  private calculateSpriteAnimationRelevance(content: string, term: string): number {
    const spriteAnimationTerms: Record<string, string[]> = {
      'sprite': ['spriteframe', 'spritebatchnode', 'animation', 'animate', 'texture', 'atlas'],
      'animation': ['spriteframe', 'animate', 'action', 'sequence', 'frame', 'duration'],
      'frame': ['spriteframe', 'animation', 'sequence', 'texture', 'atlas'],
      'sheet': ['spriteframe', 'texture', 'atlas', 'plist', 'animation'],
      'atlas': ['texture', 'spriteframe', 'sheet', 'plist'],
      'plist': ['spriteframe', 'texture', 'atlas', 'sheet'],
      '精灵': ['spriteframe', 'animation', 'texture'],
      '动画': ['spriteframe', 'animate', 'action', 'frame'],
      '图集': ['spriteframe', 'texture', 'atlas', 'plist'],
      '帧': ['frame', 'spriteframe', 'animation']
    };

    if (spriteAnimationTerms[term]) {
      let relatedScore = 0;
      spriteAnimationTerms[term].forEach((relatedTerm: string) => {
        if (content.includes(relatedTerm)) {
          relatedScore += 2.0; // 精灵动画相关术语高权重
        }
      });
      return relatedScore;
    }

    return 0;
  }

  /**
   * 判断查询是否与游戏引擎相关
   */
  private isGameEngineRelated(query: string): boolean {
    const gameEngineKeywords = [
      'sprite', 'animation', 'scene', 'texture', 'sound', 'physics', 'ui',
      'camera', 'light', 'mesh', 'render', 'game', 'engine', '2d', '3d',
      'cocos', 'axmol', 'opengl', 'metal', 'shader', 'vertex', 'fragment',
      'frame', 'sheet', 'atlas', 'plist', '精灵', '动画', '图集', '帧'
    ];

    const queryLower = query.toLowerCase();
    return gameEngineKeywords.some(keyword => queryLower.includes(keyword));
  }

  /**
   * 判断查询是否与精灵动画相关
   */
  private isSpriteAnimationRelated(query: string): boolean {
    const spriteAnimationKeywords = [
      'sprite', 'animation', 'animate', 'frame', 'sheet', 'atlas', 'plist',
      'spriteframe', 'spritebatchnode', 'texture', 'sequence', 'duration',
      '精灵', '动画', '图集', '帧', '角色', '播放'
    ];

    const queryLower = query.toLowerCase();
    return spriteAnimationKeywords.some(keyword => queryLower.includes(keyword));
  }

  /**
   * 提取匹配的关键词
   */
  private extractMatchedTerms(content: string, query: string): string[] {
    const contentLower = content.toLowerCase();
    const queryTerms = this.extractSearchTerms(query);
    const matchedTerms: string[] = [];

    queryTerms.forEach(term => {
      if (term.length >= 2) {
        // 精确匹配
        if (contentLower.includes(term)) {
          matchedTerms.push(term);
        }
        // 部分匹配
        else if (term.length > 3) {
          const partialTerm = term.substring(0, term.length - 1);
          if (contentLower.includes(partialTerm)) {
            matchedTerms.push(partialTerm + '*');
          }
        }
        // 游戏引擎相关术语的间接匹配
        else if (this.calculateGameEngineRelevance(contentLower, term) > 0) {
          matchedTerms.push(term + '(相关)');
        }
      }
    });

    return [...new Set(matchedTerms)]; // 去重
  }

  /**
   * 按相关性排序结果
   */
  private sortByRelevance(results: AxmolResource[], query: string): AxmolResource[] {
    return results.sort((a, b) => {
      // 首先按相关性分数排序
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore;
      }
      
      // 然后按匹配关键词数量排序
      if (a.matchedTerms.length !== b.matchedTerms.length) {
        return b.matchedTerms.length - a.matchedTerms.length;
      }
      
      // 最后按源类型优先级排序（官方文档优先）
      const sourcePriority = { 'official_docs': 1, 'api_page': 2, 'wiki': 3 };
      const aPriority = sourcePriority[a.source as keyof typeof sourcePriority] || 10;
      const bPriority = sourcePriority[b.source as keyof typeof sourcePriority] || 10;
      
      return aPriority - bPriority;
    });
  }

  /**
   * 分析查询意图和类型
   */
  private analyzeQuery(query: string): {
    queryType: 'documentation' | 'api' | 'examples' | 'issues' | 'community';
    keywords: string[];
    expandedKeywords: string[];
    intent: 'how-to' | 'definition' | 'troubleshooting' | 'reference';
    language?: 'cpp' | 'lua';
  } {
    const queryLower = query.toLowerCase();
    const keywords = this.extractSearchTerms(query);
    
    // 判断查询类型
    let queryType: 'documentation' | 'api' | 'examples' | 'issues' | 'community' = 'documentation';
    if (queryLower.includes('api') || queryLower.includes('method') || queryLower.includes('function')) {
      queryType = 'api';
    } else if (queryLower.includes('example') || queryLower.includes('demo') || queryLower.includes('sample')) {
      queryType = 'examples';
    } else if (queryLower.includes('error') || queryLower.includes('problem') || queryLower.includes('issue')) {
      queryType = 'issues';
    } else if (queryLower.includes('discuss') || queryLower.includes('community') || queryLower.includes('forum')) {
      queryType = 'community';
    }
    
    // 判断查询意图
    let intent: 'how-to' | 'definition' | 'troubleshooting' | 'reference' = 'reference';
    if (queryLower.includes('how') || queryLower.includes('如何') || queryLower.includes('怎么')) {
      intent = 'how-to';
    } else if (queryLower.includes('what') || queryLower.includes('什么是') || queryLower.includes('定义')) {
      intent = 'definition';
    } else if (queryLower.includes('error') || queryLower.includes('problem') || queryLower.includes('错误')) {
      intent = 'troubleshooting';
    }
    
    // 扩展关键词
    const expandedKeywords = this.expandKeywords(keywords);
    
    // 检测语言偏好
    let language: 'cpp' | 'lua' | undefined;
    if (queryLower.includes('c++') || queryLower.includes('cpp')) {
      language = 'cpp';
    } else if (queryLower.includes('lua')) {
      language = 'lua';
    }
    
    return {
      queryType,
      keywords,
      expandedKeywords,
      intent,
      language
    };
  }
  
  /**
   * 扩展关键词
   */
  private expandKeywords(keywords: string[]): string[] {
    const expanded = new Set(keywords);
    
    keywords.forEach(keyword => {
      const relatedTerms = this.enhancedKeywordMap[keyword.toLowerCase()];
      if (relatedTerms) {
        relatedTerms.forEach((term: string) => expanded.add(term));
      }
      
      // 添加常见变体
      if (keyword.endsWith('s')) {
        expanded.add(keyword.slice(0, -1)); // 去除复数
      } else {
        expanded.add(keyword + 's'); // 添加复数
      }
    });
    
    return Array.from(expanded);
  }
  
  /**
   * 增强的相关性计算
   */
  private calculateEnhancedRelevance(
    content: string, 
    query: string, 
    analysis: ReturnType<typeof this.analyzeQuery>
  ): number {
    const contentLower = content.toLowerCase();
    let score = 0;
    
    // 基础关键词匹配
    analysis.keywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      if (keywordLower.length < 2) return;
      
      // 精确匹配（高权重）
      const exactMatches = contentLower.match(new RegExp(`\\\\b${keywordLower}\\\\b`, 'gi'));
      if (exactMatches) {
        score += exactMatches.length * 10;
      }
      
      // 部分匹配（中权重）
      const partialMatches = contentLower.match(new RegExp(keywordLower, 'gi'));
      if (partialMatches && !exactMatches) {
        score += partialMatches.length * 5;
      }
    });
    
    // 扩展关键词匹配（低权重）
    analysis.expandedKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      if (analysis.keywords.includes(keyword)) return; // 避免重复计算
      
      const matches = contentLower.match(new RegExp(`\\\\b${keywordLower}\\\\b`, 'gi'));
      if (matches) {
        score += matches.length * 3;
      }
    });
    
    // 语义相关性加分
    const semanticScore = this.calculateSemanticRelevance(contentLower, analysis);
    score += semanticScore;
    
    // 查询意图匹配加分
    const intentScore = this.calculateIntentRelevance(contentLower, analysis.intent);
    score += intentScore;
    
    return Math.min(score, 100); // 限制最大分数
  }
  
  /**
   * 计算语义相关性
   */
  private calculateSemanticRelevance(
    content: string, 
    analysis: ReturnType<typeof this.analyzeQuery>
  ): number {
    let semanticScore = 0;
    
    // 基于查询类型的语义词汇
    const semanticTerms: { [key: string]: string[] } = {
      'api': ['method', 'function', 'parameter', 'return', 'class', 'interface'],
      'examples': ['example', 'demo', 'sample', 'tutorial', 'code', 'implementation'],
      'documentation': ['guide', 'manual', 'documentation', 'reference', 'overview'],
      'issues': ['error', 'problem', 'issue', 'bug', 'fix', 'solution'],
      'community': ['discussion', 'question', 'answer', 'forum', 'community']
    };
    
    const relevantTerms = semanticTerms[analysis.queryType] || [];
    relevantTerms.forEach(term => {
      if (content.includes(term)) {
        semanticScore += 2;
      }
    });
    
    return semanticScore;
  }
  
  /**
   * 计算意图相关性
   */
  private calculateIntentRelevance(content: string, intent: string): number {
    const intentTerms: { [key: string]: string[] } = {
      'how-to': ['how to', 'step', 'guide', 'tutorial', 'instruction'],
      'definition': ['definition', 'what is', 'description', 'overview'],
      'troubleshooting': ['error', 'problem', 'troubleshoot', 'fix', 'solution'],
      'reference': ['reference', 'api', 'specification', 'manual']
    };
    
    const terms = intentTerms[intent] || [];
    return terms.reduce((score, term) => {
      return score + (content.includes(term) ? 3 : 0);
    }, 0);
  }
  
  /**
   * 增强的匹配词汇提取
   */
  private extractEnhancedMatchedTerms(
    content: string, 
    query: string, 
    analysis: ReturnType<typeof this.analyzeQuery>
  ): string[] {
    const contentLower = content.toLowerCase();
    const matchedTerms: string[] = [];
    
    // 检查所有相关关键词
    const allKeywords = [...analysis.keywords, ...analysis.expandedKeywords];
    
    allKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      if (keywordLower.length >= 2) {
        // 精确匹配
        if (contentLower.includes(keywordLower)) {
          matchedTerms.push(keyword);
        }
        // 模糊匹配（相似度 > 0.8）
        else {
          const words = contentLower.split(/\\s+/);
          words.forEach(word => {
            if (this.calculateStringSimilarity(word, keywordLower) > 0.8) {
              matchedTerms.push(keyword + '(模糊)');
            }
          });
        }
      }
    });
    
    return [...new Set(matchedTerms)]; // 去重
  }
  
  /**
   * 计算字符串相似度
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }
  
  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 获取文档搜索统计信息
   */
  async getSearchStats(): Promise<{
    totalSearches: number;
    cacheHitRate: number;
    averageResponseTime: number;
    topQueries: string[];
  }> {
    // 这里可以实现搜索统计逻辑
    return {
      totalSearches: 0,
      cacheHitRate: 0,
      averageResponseTime: 0,
      topQueries: []
    };
  }
}

// 导出默认文档搜索服务实例
export const documentationService = new DocumentationService();
