/**
 * 数据源管理器
 * 管理多个数据源的优先级、可用性和负载均衡
 */

import { DataSourceConfig, AxmolResource } from '../types/index.js';
import { networkUtils } from './networkUtils.js';
import { errorHandler } from './errorHandler.js';

export class DataSourceManager {
  private dataSources: Map<string, DataSourceConfig> = new Map();
  private healthStatus: Map<string, { isHealthy: boolean; lastCheck: number; failureCount: number }> = new Map();
  private readonly healthCheckInterval = 5 * 60 * 1000; // 5分钟

  constructor() {
    this.initializeDataSources();
    this.startHealthChecks();
  }

  /**
   * 初始化数据源配置
   */
  private initializeDataSources(): void {
    const sources: DataSourceConfig[] = [
      // 官方权威资源 (优先级 1-3)
      {
        name: 'axmol_official_docs',
        priority: 1,
        enabled: true,
        baseUrl: 'https://axmol.dev/manual/latest',
        rateLimit: { requests: 60, window: 60000 },
        timeout: 12000,
        retries: 3,
        reliability: 10
      },
      {
        name: 'axmol_api_reference',
        priority: 2,
        enabled: true,
        baseUrl: 'https://axmol.dev/api',
        rateLimit: { requests: 60, window: 60000 },
        timeout: 12000,
        retries: 3,
        reliability: 10
      },
      {
        name: 'axmol_official_examples',
        priority: 3,
        enabled: true,
        baseUrl: 'https://github.com/axmolengine/axmol-examples',
        rateLimit: { requests: 50, window: 60000 },
        timeout: 10000,
        retries: 2,
        reliability: 9
      },
      
      // GitHub官方仓库资源 (优先级 4-6)
      {
        name: 'github_axmol_repo',
        priority: 4,
        enabled: true,
        baseUrl: 'https://api.github.com/repos/axmolengine/axmol',
        rateLimit: { requests: 60, window: 3600000 }, // GitHub API限制
        timeout: 15000,
        retries: 2,
        reliability: 9
      },
      {
        name: 'github_axmol_wiki',
        priority: 5,
        enabled: true,
        baseUrl: 'https://github.com/axmolengine/axmol/wiki',
        rateLimit: { requests: 40, window: 60000 },
        timeout: 10000,
        retries: 2,
        reliability: 8
      },
      {
        name: 'github_raw_content',
        priority: 6,
        enabled: true,
        baseUrl: 'https://raw.githubusercontent.com/axmolengine/axmol',
        rateLimit: { requests: 50, window: 60000 },
        timeout: 8000,
        retries: 2,
        reliability: 8
      },
      
      // 社区资源 (优先级 7-9)
      {
        name: 'axmol_discussions',
        priority: 7,
        enabled: true,
        baseUrl: 'https://github.com/axmolengine/axmol/discussions',
        rateLimit: { requests: 30, window: 60000 },
        timeout: 10000,
        retries: 2,
        reliability: 7
      },
      {
        name: 'cocos_community_forum',
        priority: 8,
        enabled: true,
        baseUrl: 'https://discuss.cocos2d-x.org',
        rateLimit: { requests: 20, window: 60000 },
        timeout: 12000,
        retries: 1,
        reliability: 6
      },
      {
        name: 'stackoverflow_axmol',
        priority: 9,
        enabled: true,
        baseUrl: 'https://stackoverflow.com/questions/tagged/axmol',
        rateLimit: { requests: 15, window: 60000 },
        timeout: 12000,
        retries: 1,
        reliability: 6
      },
      
      // 备用搜索 (优先级 10)
      {
        name: 'web_search_fallback',
        priority: 10,
        enabled: true,
        baseUrl: 'https://www.google.com/search',
        rateLimit: { requests: 10, window: 60000 },
        timeout: 12000,
        retries: 1,
        reliability: 4
      }
    ];

    sources.forEach(source => {
      this.dataSources.set(source.name, source);
      this.healthStatus.set(source.name, {
        isHealthy: true,
        lastCheck: 0,
        failureCount: 0
      });
    });

    console.log(`📊 初始化了 ${sources.length} 个数据源`);
  }

  /**
   * 获取可用的数据源列表（按优先级排序）
   */
  getAvailableDataSources(): DataSourceConfig[] {
    return Array.from(this.dataSources.values())
      .filter(source => source.enabled && this.isHealthy(source.name))
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * 获取特定数据源配置
   */
  getDataSource(name: string): DataSourceConfig | undefined {
    return this.dataSources.get(name);
  }

  /**
   * 检查数据源是否健康
   */
  isHealthy(sourceName: string): boolean {
    const health = this.healthStatus.get(sourceName);
    return health ? health.isHealthy : false;
  }

  /**
   * 标记数据源为不健康
   */
  markUnhealthy(sourceName: string, error?: any): void {
    const health = this.healthStatus.get(sourceName);
    if (health) {
      health.isHealthy = false;
      health.failureCount++;
      health.lastCheck = Date.now();
      
      console.log(`❌ 数据源 ${sourceName} 标记为不健康 (失败次数: ${health.failureCount})`);
      
      if (error) {
        errorHandler.createError(
          'DATA_SOURCE_UNHEALTHY',
          `数据源 ${sourceName} 不可用`,
          { error: error.message, failureCount: health.failureCount },
          'DataSourceManager'
        );
      }
    }
  }

  /**
   * 标记数据源为健康
   */
  markHealthy(sourceName: string): void {
    const health = this.healthStatus.get(sourceName);
    if (health) {
      const wasUnhealthy = !health.isHealthy;
      health.isHealthy = true;
      health.failureCount = 0;
      health.lastCheck = Date.now();
      
      if (wasUnhealthy) {
        console.log(`✅ 数据源 ${sourceName} 恢复健康`);
      }
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(sourceName: string): Promise<boolean> {
    const source = this.dataSources.get(sourceName);
    if (!source) return false;

    try {
      // 根据数据源类型执行不同的健康检查
      let isHealthy = false;

      switch (sourceName) {
        case 'axmol_official_docs':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        case 'axmol_api_reference':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        case 'axmol_official_examples':
          isHealthy = await networkUtils.checkUrl(`${source.baseUrl}/blob/main/README.md`);
          break;
        
        case 'github_axmol_repo':
          isHealthy = await networkUtils.checkUrl(`${source.baseUrl}/readme`);
          break;
        
        case 'github_axmol_wiki':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        case 'github_raw_content':
          isHealthy = await networkUtils.checkUrl(`${source.baseUrl}/dev/README.md`);
          break;
        
        case 'axmol_discussions':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        case 'cocos_community_forum':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        case 'stackoverflow_axmol':
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
          break;
        
        default:
          isHealthy = await networkUtils.checkUrl(source.baseUrl);
      }

      if (isHealthy) {
        this.markHealthy(sourceName);
      } else {
        this.markUnhealthy(sourceName);
      }

      return isHealthy;
    } catch (error) {
      this.markUnhealthy(sourceName, error);
      return false;
    }
  }

  /**
   * 启动定期健康检查
   */
  private startHealthChecks(): void {
    setInterval(async () => {
      console.log('🔍 开始数据源健康检查...');
      
      const checkPromises = Array.from(this.dataSources.keys()).map(async (sourceName) => {
        const health = this.healthStatus.get(sourceName);
        
        // 如果数据源不健康且距离上次检查超过健康检查间隔，则重新检查
        if (health && (!health.isHealthy || Date.now() - health.lastCheck > this.healthCheckInterval)) {
          return this.performHealthCheck(sourceName);
        }
        
        return health?.isHealthy || false;
      });

      const results = await Promise.allSettled(checkPromises);
      const healthyCount = results.filter(result => 
        result.status === 'fulfilled' && result.value
      ).length;

      console.log(`📊 健康检查完成: ${healthyCount}/${this.dataSources.size} 个数据源健康`);
    }, this.healthCheckInterval);
  }

  /**
   * 获取数据源统计信息
   */
  getStats(): {
    total: number;
    healthy: number;
    unhealthy: number;
    sources: Array<{
      name: string;
      priority: number;
      enabled: boolean;
      healthy: boolean;
      failureCount: number;
      lastCheck: string;
    }>;
  } {
    const sources = Array.from(this.dataSources.entries()).map(([name, config]) => {
      const health = this.healthStatus.get(name)!;
      return {
        name,
        priority: config.priority,
        enabled: config.enabled,
        healthy: health.isHealthy,
        failureCount: health.failureCount,
        lastCheck: health.lastCheck > 0 ? new Date(health.lastCheck).toISOString() : 'Never'
      };
    });

    const healthy = sources.filter(s => s.healthy && s.enabled).length;
    const unhealthy = sources.filter(s => !s.healthy && s.enabled).length;

    return {
      total: sources.length,
      healthy,
      unhealthy,
      sources: sources.sort((a, b) => a.priority - b.priority)
    };
  }

  /**
   * 启用数据源
   */
  enableDataSource(sourceName: string): boolean {
    const source = this.dataSources.get(sourceName);
    if (source) {
      source.enabled = true;
      console.log(`✅ 数据源 ${sourceName} 已启用`);
      return true;
    }
    return false;
  }

  /**
   * 禁用数据源
   */
  disableDataSource(sourceName: string): boolean {
    const source = this.dataSources.get(sourceName);
    if (source) {
      source.enabled = false;
      console.log(`❌ 数据源 ${sourceName} 已禁用`);
      return true;
    }
    return false;
  }

  /**
   * 更新数据源配置
   */
  updateDataSource(sourceName: string, updates: Partial<DataSourceConfig>): boolean {
    const source = this.dataSources.get(sourceName);
    if (source) {
      Object.assign(source, updates);
      console.log(`🔧 数据源 ${sourceName} 配置已更新`);
      return true;
    }
    return false;
  }

  /**
   * 获取推荐的数据源（基于健康状态、优先级和可靠性）
   */
  getRecommendedDataSource(sourceType?: 'official' | 'github' | 'community'): DataSourceConfig | null {
    let available = this.getAvailableDataSources();
    
    // 根据类型过滤
    if (sourceType) {
      available = available.filter(source => {
        switch (sourceType) {
          case 'official':
            return source.name.includes('axmol_official') || source.name.includes('axmol_api');
          case 'github':
            return source.name.includes('github');
          case 'community':
            return source.name.includes('discussions') || source.name.includes('forum') || source.name.includes('stackoverflow');
          default:
            return true;
        }
      });
    }
    
    // 按可靠性和优先级排序
    available.sort((a, b) => {
      const reliabilityDiff = (b.reliability || 0) - (a.reliability || 0);
      return reliabilityDiff !== 0 ? reliabilityDiff : a.priority - b.priority;
    });
    
    return available.length > 0 ? available[0] : null;
  }
  
  /**
   * 根据查询类型获取最佳数据源组合
   */
  getOptimalDataSources(queryType: 'documentation' | 'api' | 'examples' | 'issues' | 'community'): DataSourceConfig[] {
    const all = this.getAvailableDataSources();
    
    switch (queryType) {
      case 'documentation':
        return all.filter(s => 
          s.name.includes('official_docs') || 
          s.name.includes('wiki') || 
          s.name.includes('api_reference')
        );
      
      case 'api':
        return all.filter(s => 
          s.name.includes('api_reference') || 
          s.name.includes('official_docs') || 
          s.name.includes('raw_content')
        );
      
      case 'examples':
        return all.filter(s => 
          s.name.includes('examples') || 
          s.name.includes('github_axmol_repo') || 
          s.name.includes('raw_content')
        );
      
      case 'issues':
        return all.filter(s => 
          s.name.includes('github') || 
          s.name.includes('discussions') || 
          s.name.includes('stackoverflow')
        );
      
      case 'community':
        return all.filter(s => 
          s.name.includes('discussions') || 
          s.name.includes('forum') || 
          s.name.includes('stackoverflow')
        );
      
      default:
        return all;
    }
  }

  /**
   * 获取备用数据源列表
   */
  getFallbackDataSources(excludeSource?: string): DataSourceConfig[] {
    return this.getAvailableDataSources()
      .filter(source => source.name !== excludeSource);
  }

  /**
   * 重置数据源健康状态
   */
  resetHealthStatus(sourceName?: string): void {
    if (sourceName) {
      const health = this.healthStatus.get(sourceName);
      if (health) {
        health.isHealthy = true;
        health.failureCount = 0;
        health.lastCheck = 0;
        console.log(`🔄 数据源 ${sourceName} 健康状态已重置`);
      }
    } else {
      this.healthStatus.forEach((health, name) => {
        health.isHealthy = true;
        health.failureCount = 0;
        health.lastCheck = 0;
      });
      console.log('🔄 所有数据源健康状态已重置');
    }
  }

  /**
   * 获取数据源的URL构建器
   */
  buildUrl(sourceName: string, path: string): string {
    const source = this.dataSources.get(sourceName);
    if (!source) {
      throw new Error(`未知的数据源: ${sourceName}`);
    }

    const baseUrl = source.baseUrl.endsWith('/') ? source.baseUrl.slice(0, -1) : source.baseUrl;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    
    return `${baseUrl}${cleanPath}`;
  }
}

// 导出默认数据源管理器实例
export const dataSourceManager = new DataSourceManager();
