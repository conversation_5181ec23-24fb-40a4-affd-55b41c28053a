/**
 * 构建问题诊断服务
 * 负责诊断和解决 Axmol 构建相关问题
 */

import { BuildIssue, BuildSolution, CodeChange, ConfigChange, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

export class EnhancedBuildIssueService {
  private readonly GITHUB_API_BASE = 'https://api.github.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1小时缓存

  // 增强的构建问题知识库
  private readonly enhancedKnownIssues: Map<string, BuildSolution[]> = new Map();
  private readonly errorPatterns: Map<RegExp, string> = new Map();
  private readonly platformSpecificIssues: Map<string, string[]> = new Map();
  
  // 智能诊断引擎
  private readonly diagnosticEngine = {
    errorClassifiers: new Map<string, (error: string) => number>(),
    solutionRankers: new Map<string, (solutions: BuildSolution[]) => BuildSolution[]>(),
    contextAnalyzers: new Map<string, (context: any) => any>()
  };

  constructor() {
    this.initializeEnhancedKnowledgeBase();
    this.initializeErrorPatterns();
    this.initializePlatformSpecificIssues();
    this.initializeDiagnosticEngine();
  }

  /**
   * 解决构建问题
   */
  async solveBuildIssue(
    platform: string,
    errorMessage: string,
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔧 诊断构建问题: ${platform} - ${errorMessage.substring(0, 100)}...`);

      // 生成缓存键
      const cacheKey = `build_issue_${platform}_${this.hashString(errorMessage)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as BuildIssue | null;
        if (cached) {
          console.log('✅ 从缓存获取构建问题解决方案');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.solutions.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];
      const solutions: BuildSolution[] = [];

      // 1. 检查已知问题库
      const knownSolutions = this.searchKnownIssues(platform, errorMessage);
      if (knownSolutions.length > 0) {
        solutions.push(...knownSolutions);
        sources.push('known_issues');
      }

      // 2. 搜索GitHub Issues
      const githubSolutions = await this.searchGitHubIssues(platform, errorMessage);
      if (githubSolutions.length > 0) {
        solutions.push(...githubSolutions);
        sources.push('github_issues');
      }

      // 3. 搜索社区讨论
      const communitySolutions = await this.searchCommunityDiscussions(platform, errorMessage);
      if (communitySolutions.length > 0) {
        solutions.push(...communitySolutions);
        sources.push('community');
      }

      // 4. 生成通用解决方案
      if (solutions.length === 0) {
        const genericSolutions = this.generateGenericSolutions(platform, errorMessage);
        solutions.push(...genericSolutions);
        sources.push('generic');
      }

      // 按优先级排序解决方案
      const sortedSolutions = this.sortSolutionsByPriority(solutions);

      const buildIssue: BuildIssue = {
        platform,
        errorMessage,
        errorType: this.classifyErrorType(errorMessage),
        solutions: sortedSolutions,
        relatedIssues: await this.findRelatedIssues(platform, errorMessage)
      };

      // 缓存结果
      if (options.useCache !== false && solutions.length > 0) {
        await defaultCache.set(cacheKey, buildIssue, this.CACHE_TTL);
      }

      console.log(`✅ 构建问题诊断完成: 找到 ${solutions.length} 个解决方案`);

      return {
        success: true,
        data: buildIssue,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: solutions.length,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'solveBuildIssue', { platform, errorMessage, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 初始化增强的知识库
   */
  private initializeEnhancedKnowledgeBase(): void {
    // Android 构建问题（大幅扩展）
    this.enhancedKnownIssues.set('android', [
      {
        title: 'Android Gradle Plugin版本不兼容',
        description: 'Gradle版本与Android Gradle Plugin版本不匹配',
        steps: [
          '检查gradle/wrapper/gradle-wrapper.properties中的Gradle版本',
          '检查build.gradle中的Android Gradle Plugin版本',
          '参考兼容性矩阵更新版本',
          '清理项目并重新构建: ./gradlew clean build'
        ],
        codeChanges: [],
        configChanges: [
          {
            file: 'gradle-wrapper.properties',
            setting: 'distributionUrl',
            value: 'https\\://services.gradle.org/distributions/gradle-7.6-all.zip',
            explanation: '使用推荐的Gradle版本'
          }
        ],
        priority: 'high',
        verified: true
      },
      {
        title: 'NDK路径配置错误',
        description: 'NDK路径未正确配置或NDK版本不兼容',
        steps: [
          '检查Android SDK Manager中NDK安装状态',
          '在local.properties中配置正确的ndk.dir路径',
          '确认使用推荐的NDK版本（r23c-r25c）',
          '清理并重新构建项目'
        ],
        codeChanges: [],
        configChanges: [
          {
            file: 'local.properties',
            setting: 'ndk.dir',
            value: 'C\\:\\Users\\{username}\\AppData\\Local\\Android\\Sdk\\ndk\\23.2.8568313',
            explanation: '设置正确的NDK路径'
          }
        ],
        priority: 'high',
        verified: true
      },
      {
        title: 'Java版本不兼容',
        description: 'JDK版本与Android构建要求不匹配',
        steps: [
          '检查当前Java版本: java -version',
          '安装JDK 11或JDK 17（推荐）',
          '设置JAVA_HOME环境变量',
          '在Android Studio中配置正确的JDK路径'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: true
      }
    ]);

    // iOS 构建问题（大幅扩展）
    this.enhancedKnownIssues.set('ios', [
      {
        title: 'Xcode版本兼容性问题',
        description: 'Xcode版本与Axmol要求不匹配',
        steps: [
          '检查Axmol文档中的Xcode版本要求',
          '更新到推荐的Xcode版本（通常是最新稳定版）',
          '清理Derived Data: ~/Library/Developer/Xcode/DerivedData',
          '重新生成Xcode项目文件'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'high',
        verified: true
      },
      {
        title: 'iOS部署目标版本过低',
        description: 'iOS Deployment Target版本低于Axmol要求',
        steps: [
          '在Xcode项目设置中找到Deployment Info',
          '将iOS Deployment Target设置为11.0或更高',
          '确保所有Target的部署目标版本一致',
          '重新构建项目'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: true
      }
    ]);

    // Windows 构建问题（大幅扩展）
    this.enhancedKnownIssues.set('windows', [
      {
        title: 'Visual Studio版本不兼容',
        description: 'Visual Studio版本过旧或缺少必要组件',
        steps: [
          '检查Axmol文档中的Visual Studio版本要求',
          '安装Visual Studio 2019或2022（推荐Community版本）',
          '确保安装了C++桌面开发工作负载',
          '安装Windows 10/11 SDK'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'high',
        verified: true
      },
      {
        title: 'CMake生成器选择错误',
        description: 'CMake使用了错误的生成器或平台',
        steps: [
          '删除build目录中的所有文件',
          '使用正确的CMake命令重新生成',
          '对于VS2019: cmake .. -G "Visual Studio 16 2019" -A x64',
          '对于VS2022: cmake .. -G "Visual Studio 17 2022" -A x64'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: true
      }
    ]);

    // macOS 构建问题
    this.enhancedKnownIssues.set('macos', [
      {
        title: 'Homebrew依赖配置问题',
        description: 'macOS上Homebrew安装的依赖库路径问题',
        steps: [
          '确保Homebrew已正确安装: brew --version',
          '安装必要的依赖: brew install cmake pkg-config',
          '更新CMake查找路径以包含Homebrew库',
          '设置环境变量: export PKG_CONFIG_PATH="/opt/homebrew/lib/pkgconfig"'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: true
      }
    ]);

    // Linux 构建问题
    this.enhancedKnownIssues.set('linux', [
      {
        title: '依赖库缺失',
        description: 'Linux系统缺少必要的开发库',
        steps: [
          '更新软件包列表: sudo apt update',
          '安装必要的开发工具: sudo apt install build-essential cmake',
          '安装图形库: sudo apt install libgl1-mesa-dev libglu1-mesa-dev',
          '安装音频库: sudo apt install libasound2-dev'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'high',
        verified: true
      }
    ]);

    console.log(`📚 初始化了 ${this.enhancedKnownIssues.size} 个增强问题类别`);
  }
  
  /**
   * 初始化错误模式匹配
   */
  private initializeErrorPatterns(): void {
    // 编译错误模式
    this.errorPatterns.set(/error:\s*([^:]+\.(?:h|hpp|cpp|c|cc)):\s*(\d+):\s*(.+)/i, 'compile_file_error');
    this.errorPatterns.set(/undefined reference to\s*[`'"]([^'"]+)[`'"]/i, 'link_undefined_reference');
    this.errorPatterns.set(/cannot find\s+(-l\w+|\w+\.(?:so|a|lib))/i, 'link_library_not_found');
    this.errorPatterns.set(/No such file or directory[^:]*:\s*([^\n]+)/i, 'file_not_found');
    
    // 平台特定错误模式
    this.errorPatterns.set(/Android NDK.*not found/i, 'android_ndk_missing');
    this.errorPatterns.set(/Xcode.*not found|xcodebuild.*failed/i, 'ios_xcode_error');
    this.errorPatterns.set(/MSVC.*not found|Visual Studio.*error/i, 'windows_msvc_error');
    this.errorPatterns.set(/ld:\s*library not found for\s+-l(\w+)/i, 'macos_library_not_found');
    
    // 配置错误模式
    this.errorPatterns.set(/CMake Error.*CMAKE_\w+_COMPILER/i, 'cmake_compiler_error');
    this.errorPatterns.set(/Could not find a package configuration file provided by\s+(\w+)/i, 'cmake_package_missing');
    
    console.log(`🔍 初始化了 ${this.errorPatterns.size} 个错误模式`);
  }
  
  /**
   * 初始化平台特定问题
   */
  private initializePlatformSpecificIssues(): void {
    this.platformSpecificIssues.set('android', ['android']);
    this.platformSpecificIssues.set('ios', ['ios']);
    this.platformSpecificIssues.set('windows', ['windows']);
    this.platformSpecificIssues.set('macos', ['macos']);
    this.platformSpecificIssues.set('linux', ['linux']);
  }
  
  /**
   * 初始化智能诊断引擎
   */
  private initializeDiagnosticEngine(): void {
    // 错误分类器
    this.diagnosticEngine.errorClassifiers.set('severity', (error: string) => {
      if (error.toLowerCase().includes('fatal') || error.toLowerCase().includes('critical')) return 10;
      if (error.toLowerCase().includes('error')) return 8;
      if (error.toLowerCase().includes('warning')) return 5;
      return 3;
    });
    
    // 解决方案排序器
    this.diagnosticEngine.solutionRankers.set('confidence', (solutions: BuildSolution[]) => {
      return solutions.sort((a, b) => {
        if (a.verified !== b.verified) return a.verified ? -1 : 1;
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
    });
  }
  
  /**
   * 兼容性方法
   */
  private initializeKnownIssues(): void {
    this.initializeEnhancedKnowledgeBase();
  }
  
  private get knownIssues(): Map<string, BuildSolution[]> {
    return this.enhancedKnownIssues;
  }

  /**
   * 增强的已知问题搜索
   */
  private searchKnownIssues(platform: string, errorMessage: string): BuildSolution[] {
    const solutions: BuildSolution[] = [];
    const errorLower = errorMessage.toLowerCase();
    const platformLower = platform.toLowerCase();
    
    // 1. 智能错误模式匹配
    const matchedPatterns = this.matchErrorPatterns(errorMessage);
    
    // 2. 搜索平台特定问题
    const platformIssues = this.enhancedKnownIssues.get(platformLower);
    if (platformIssues) {
      for (const solution of platformIssues) {
        const relevanceScore = this.calculateSolutionRelevance(errorLower, solution, matchedPatterns);
        if (relevanceScore > 0.3) { // 设置相关性阈值
          solutions.push({
            ...solution,
            relevanceScore // 添加相关性分数用于排序
          } as BuildSolution & { relevanceScore: number });
        }
      }
    }
    
    // 3. 搜索通用解决方案
    const genericSolutions = this.findGenericSolutions(errorMessage, matchedPatterns);
    solutions.push(...genericSolutions);
    
    // 4. 使用智能诊断引擎排序
    const ranker = this.diagnosticEngine.solutionRankers.get('confidence');
    return ranker ? ranker(solutions) : solutions;
  }
  
  /**
   * 匹配错误模式
   */
  private matchErrorPatterns(errorMessage: string): string[] {
    const matches: string[] = [];
    
    for (const [pattern, patternType] of this.errorPatterns.entries()) {
      if (pattern.test(errorMessage)) {
        matches.push(patternType);
      }
    }
    
    return matches;
  }
  
  /**
   * 计算解决方案相关性
   */
  private calculateSolutionRelevance(
    errorMessage: string, 
    solution: BuildSolution, 
    matchedPatterns: string[]
  ): number {
    let relevance = 0;
    
    // 模式匹配加分
    if (matchedPatterns.length > 0) {
      relevance += 0.4;
    }
    
    // 关键词匹配加分
    const solutionKeywords = [
      ...solution.title.toLowerCase().split(/\s+/),
      ...solution.description.toLowerCase().split(/\s+/)
    ];
    
    const errorWords = errorMessage.toLowerCase().split(/\s+/);
    const keywordMatches = solutionKeywords.filter(keyword => 
      keyword.length > 3 && errorWords.some(word => word.includes(keyword))
    ).length;
    
    relevance += Math.min(keywordMatches * 0.1, 0.5);
    
    // 验证状态加分
    if (solution.verified) {
      relevance += 0.2;
    }
    
    // 优先级加分
    const priorityBonus = { 'high': 0.3, 'medium': 0.2, 'low': 0.1 };
    relevance += priorityBonus[solution.priority];
    
    return Math.min(relevance, 1.0);
  }
  
  /**
   * 查找通用解决方案
   */
  private findGenericSolutions(errorMessage: string, matchedPatterns: string[]): BuildSolution[] {
    const solutions: BuildSolution[] = [];
    
    // 根据错误模式提供通用建议
    if (matchedPatterns.includes('compile_file_error')) {
      solutions.push({
        title: '编译错误通用解决方案',
        description: '针对源文件编译错误的通用步骤',
        steps: [
          '检查代码语法错误',
          '确认头文件包含路径正确',
          '检查编译器版本兼容性',
          '清理并重新构建项目'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: false
      });
    }
    
    if (matchedPatterns.includes('link_undefined_reference')) {
      solutions.push({
        title: '链接错误：未定义引用',
        description: '解决未定义引用的链接错误',
        steps: [
          '检查函数声明和定义是否匹配',
          '确认相关库文件已正确链接',
          '检查命名空间和符号可见性',
          '验证库文件版本兼容性'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: false
      });
    }
    
    return solutions;
  }

  /**
   * 检查错误是否与解决方案相关
   */
  private isErrorRelevant(errorMessage: string, solution: BuildSolution): boolean {
    const solutionKeywords = [
      ...solution.title.toLowerCase().split(/\s+/),
      ...solution.description.toLowerCase().split(/\s+/)
    ];

    return solutionKeywords.some(keyword => 
      keyword.length > 3 && errorMessage.includes(keyword)
    );
  }

  /**
   * 搜索GitHub Issues
   */
  private async searchGitHubIssues(platform: string, errorMessage: string): Promise<BuildSolution[]> {
    const solutions: BuildSolution[] = [];

    try {
      // 构建搜索查询
      const searchQuery = this.buildGitHubSearchQuery(platform, errorMessage);
      const searchUrl = `${this.GITHUB_API_BASE}/search/issues?q=${encodeURIComponent(searchQuery)}&sort=updated&per_page=10`;

      const response = await networkUtils.get(searchUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 8000 // 减少超时时间
      });

      const issues = response.data.items || [];

      for (const issue of issues.slice(0, 5)) {
        const solution = await this.extractSolutionFromIssue(issue);
        if (solution) {
          solutions.push(solution);
        }
      }

      console.log(`🔍 从GitHub Issues找到 ${solutions.length} 个解决方案`);

    } catch (error) {
      console.log('⚠️ GitHub Issues搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return solutions;
  }

  /**
   * 构建GitHub搜索查询
   */
  private buildGitHubSearchQuery(platform: string, errorMessage: string): string {
    const keywords = this.extractErrorKeywords(errorMessage);
    const query = [
      `repo:${this.AXMOL_REPO}`,
      `is:issue`,
      platform,
      ...keywords.slice(0, 3)
    ].join(' ');

    return query;
  }

  /**
   * 提取错误关键词
   */
  private extractErrorKeywords(errorMessage: string): string[] {
    const keywords: string[] = [];
    
    // 提取常见的错误关键词
    const errorPatterns = [
      /error\s+(\w+)/gi,
      /undefined\s+(\w+)/gi,
      /cannot\s+find\s+(\w+)/gi,
      /missing\s+(\w+)/gi,
      /failed\s+to\s+(\w+)/gi
    ];

    errorPatterns.forEach(pattern => {
      const matches = errorMessage.match(pattern);
      if (matches) {
        keywords.push(...matches);
      }
    });

    // 添加文件扩展名和路径信息
    const fileMatches = errorMessage.match(/\w+\.(h|cpp|c|hpp|cc)/g);
    if (fileMatches) {
      keywords.push(...fileMatches);
    }

    return [...new Set(keywords)].slice(0, 5);
  }

  /**
   * 从Issue中提取解决方案
   */
  private async extractSolutionFromIssue(issue: any): Promise<BuildSolution | null> {
    try {
      // 检查Issue是否已关闭（通常意味着已解决）
      if (issue.state !== 'closed') {
        return null;
      }

      return {
        title: `GitHub Issue解决方案: ${issue.title}`,
        description: issue.body?.substring(0, 200) || '来自GitHub Issue的解决方案',
        steps: [
          '查看完整的Issue讨论',
          '按照Issue中提到的解决步骤操作',
          '如果问题仍然存在，可以在Issue中留言求助'
        ],
        codeChanges: [],
        configChanges: [],
        priority: 'medium',
        verified: false
      };

    } catch (error) {
      return null;
    }
  }

  /**
   * 搜索社区讨论
   */
  private async searchCommunityDiscussions(platform: string, errorMessage: string): Promise<BuildSolution[]> {
    // 这里可以实现搜索Discord、Reddit等社区平台的逻辑
    // 目前返回空数组，后续可以扩展
    return [];
  }

  /**
   * 生成通用解决方案
   */
  private generateGenericSolutions(platform: string, errorMessage: string): BuildSolution[] {
    const solutions: BuildSolution[] = [];
    const errorType = this.classifyErrorType(errorMessage);

    switch (errorType) {
      case 'compile':
        solutions.push({
          title: '编译错误通用解决方案',
          description: '常见的编译错误解决步骤',
          steps: [
            '检查代码语法错误',
            '确保所有头文件路径正确',
            '检查编译器版本兼容性',
            '清理并重新构建项目'
          ],
          codeChanges: [],
          configChanges: [],
          priority: 'low',
          verified: false
        });
        break;

      case 'link':
        solutions.push({
          title: '链接错误通用解决方案',
          description: '常见的链接错误解决步骤',
          steps: [
            '检查库文件是否存在',
            '确认库文件路径配置正确',
            '检查库文件版本兼容性',
            '重新生成项目文件'
          ],
          codeChanges: [],
          configChanges: [],
          priority: 'low',
          verified: false
        });
        break;

      default:
        solutions.push({
          title: '通用构建问题解决方案',
          description: '适用于大多数构建问题的通用步骤',
          steps: [
            '清理构建缓存',
            '检查依赖项是否正确安装',
            '确认开发环境配置',
            '查看详细的错误日志',
            '搜索相关的GitHub Issues'
          ],
          codeChanges: [],
          configChanges: [],
          priority: 'low',
          verified: false
        });
    }

    return solutions;
  }

  /**
   * 分类错误类型
   */
  private classifyErrorType(errorMessage: string): 'compile' | 'link' | 'runtime' | 'configuration' {
    const errorLower = errorMessage.toLowerCase();

    if (errorLower.includes('compile') || errorLower.includes('syntax') || errorLower.includes('parse')) {
      return 'compile';
    } else if (errorLower.includes('link') || errorLower.includes('undefined reference') || errorLower.includes('unresolved')) {
      return 'link';
    } else if (errorLower.includes('runtime') || errorLower.includes('crash') || errorLower.includes('exception')) {
      return 'runtime';
    } else {
      return 'configuration';
    }
  }

  /**
   * 按优先级排序解决方案
   */
  private sortSolutionsByPriority(solutions: BuildSolution[]): BuildSolution[] {
    const priorityOrder = { 'high': 1, 'medium': 2, 'low': 3 };
    
    return solutions.sort((a, b) => {
      // 首先按验证状态排序
      if (a.verified !== b.verified) {
        return a.verified ? -1 : 1;
      }
      
      // 然后按优先级排序
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }

  /**
   * 查找相关问题
   */
  private async findRelatedIssues(platform: string, errorMessage: string): Promise<string[]> {
    // 简化实现，返回一些通用的相关问题链接
    return [
      `https://github.com/${this.AXMOL_REPO}/issues?q=is%3Aissue+${platform}+build`,
      `https://github.com/${this.AXMOL_REPO}/wiki/Build-${platform}`
    ];
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
}

// 保留原始类以保持兼容性
export class BuildIssueService extends EnhancedBuildIssueService {
  constructor() {
    super();
  }
}

// 导出默认构建问题服务实例
export const buildIssueService = new EnhancedBuildIssueService();
