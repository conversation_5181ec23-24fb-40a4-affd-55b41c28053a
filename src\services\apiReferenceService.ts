/**
 * API参考服务
 * 负责获取和提供 Axmol API 参考文档
 */

import * as cheerio from 'cheerio';
import { ApiReference, ApiParameter, CodeExample, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

export class ApiReferenceService {
  private readonly OFFICIAL_DOCS_BASE = 'https://axmol.dev/manual/latest';
  private readonly OFFICIAL_API_BASE = 'https://axmol.dev/api';
  private readonly GITHUB_RAW_BASE = 'https://raw.githubusercontent.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 2 * 60 * 60 * 1000; // 2小时缓存
  
  // 增强的API类名映射
  private readonly enhancedClassMappings: { [key: string]: {
    officialName: string;
    namespace: string;
    category: string;
    headerFile: string;
    relatedClasses: string[];
    description: string;
  } } = {
    'sprite': {
      officialName: 'Sprite',
      namespace: 'ax',
      category: '2d',
      headerFile: 'core/2d/Sprite.h',
      relatedClasses: ['SpriteFrame', 'SpriteBatchNode', 'Texture2D'],
      description: '2D精灵类，用于显示图像和纹理'
    },
    'spriteframe': {
      officialName: 'SpriteFrame',
      namespace: 'ax',
      category: '2d',
      headerFile: 'core/2d/SpriteFrame.h',
      relatedClasses: ['Sprite', 'SpriteFrameCache', 'Texture2D'],
      description: '精灵帧类，包含精灵的纹理和显示信息'
    },
    'animation': {
      officialName: 'Animation',
      namespace: 'ax',
      category: '2d',
      headerFile: 'core/2d/Animation.h',
      relatedClasses: ['Animate', 'SpriteFrame', 'AnimationCache'],
      description: '动画类，管理精灵帧动画序列'
    },
    'action': {
      officialName: 'Action',
      namespace: 'ax',
      category: '2d',
      headerFile: 'core/2d/Action.h',
      relatedClasses: ['ActionManager', 'ActionInterval', 'Node'],
      description: '动作基类，用于节点动画和变换'
    },
    'node': {
      officialName: 'Node',
      namespace: 'ax',
      category: '2d',
      headerFile: 'core/2d/Node.h',
      relatedClasses: ['Scene', 'Layer', 'Sprite'],
      description: '节点基类，所有场景对象的父类'
    },
    'director': {
      officialName: 'Director',
      namespace: 'ax',
      category: 'base',
      headerFile: 'core/base/Director.h',
      relatedClasses: ['Scene', 'Scheduler', 'EventDispatcher'],
      description: '导演类，管理场景和游戏循环'
    }
  };

  /**
   * 获取API参考文档
   */
  async getApiReference(
    className: string,
    methodName?: string,
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 获取API参考: ${className}${methodName ? `::${methodName}` : ''}`);

      // 生成缓存键
      const cacheKey = `api_ref_${className}_${methodName || 'all'}_${JSON.stringify(options)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as ApiReference | null;
        if (cached) {
          console.log('✅ 从缓存获取API参考');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: 1,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];
      let apiReference: ApiReference | null = null;
      
      // 智能搜索策略：根据类名选择最佳搜索途径
      const searchStrategy = this.determineSearchStrategy(className, methodName);
      
      // 1. 尝试增强的官方API搜索
      if (searchStrategy.useOfficial) {
        apiReference = await this.getEnhancedOfficialApiReference(className, methodName);
        if (apiReference) {
          sources.push('enhanced_official_api');
        }
      }
      
      // 2. 备用：传统官方文档搜索
      if (!apiReference && searchStrategy.useTraditional) {
        apiReference = await this.getOfficialApiReference(className, methodName);
        if (apiReference) {
          sources.push('official_docs');
        }
      }

      // 3. 源码分析
      if (!apiReference && searchStrategy.useSourceCode) {
        apiReference = await this.getEnhancedSourceCodeApiReference(className, methodName);
        if (apiReference) {
          sources.push('enhanced_source_code');
        }
      }

      // 4. 相关类搜索
      if (!apiReference && searchStrategy.useRelated) {
        apiReference = await this.searchRelatedClassesApi(className, methodName);
        if (apiReference) {
          sources.push('related_classes');
        }
      }

      // 5. 智能生成
      if (!apiReference) {
        apiReference = await this.generateEnhancedApiReference(className, methodName);
        if (apiReference) {
          sources.push('enhanced_generated');
        }
      }

      if (!apiReference) {
        throw new Error(`未找到 ${className} 的API参考文档`);
      }

      // 缓存结果
      if (options.useCache !== false) {
        await defaultCache.set(cacheKey, apiReference, this.CACHE_TTL);
      }

      console.log(`✅ API参考获取完成: ${className}`);

      return {
        success: true,
        data: apiReference,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 1,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'getApiReference', { className, methodName, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 确定搜索策略
   */
  private determineSearchStrategy(className: string, methodName?: string): {
    useOfficial: boolean;
    useTraditional: boolean;
    useSourceCode: boolean;
    useRelated: boolean;
    priority: string[];
  } {
    const classInfo = this.enhancedClassMappings[className.toLowerCase()];
    
    // 根据类型和类别决定策略
    const strategy = {
      useOfficial: true,
      useTraditional: true,
      useSourceCode: true,
      useRelated: true,
      priority: ['official', 'source', 'related', 'generated']
    };
    
    if (classInfo) {
      // 核心类优先使用官方API
      if (classInfo.category === 'base' || classInfo.category === '2d') {
        strategy.priority = ['official', 'enhanced_official', 'source', 'related'];
      }
      // 3D类优先使用源码分析
      else if (classInfo.category === '3d') {
        strategy.priority = ['source', 'official', 'related', 'generated'];
      }
    }
    
    return strategy;
  }
  
  /**
   * 增强的官方API参考获取
   */
  private async getEnhancedOfficialApiReference(className: string, methodName?: string): Promise<ApiReference | null> {
    try {
      // 尝试新的API基地址
      const apiBaseUrl = this.OFFICIAL_API_BASE;
      const classInfo = this.enhancedClassMappings[className.toLowerCase()];
      
      if (classInfo) {
        const apiUrl = `${apiBaseUrl}/${classInfo.category}/${classInfo.officialName}.html`;
        
        try {
          const response = await networkUtils.get(apiUrl, {
            timeout: 10000,
            validateStatus: (status) => status === 200
          });
          
          const apiRef = this.parseEnhancedApiPage(response.data, className, methodName, apiUrl, classInfo);
          if (apiRef) {
            console.log(`✅ 从增强官方API获取: ${className}`);
            return apiRef;
          }
        } catch (error) {
          console.log(`⚠️ 增强官方API访问失败: ${apiUrl}`);
        }
      }
      
      // 备用：使用传统方式
      return this.getOfficialApiReference(className, methodName);
      
    } catch (error) {
      console.log('⚠️ 增强官方API获取失败:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }
  
  /**
   * 解析增强的API页面
   */
  private parseEnhancedApiPage(
    content: string,
    className: string,
    methodName: string | undefined,
    url: string,
    classInfo: any
  ): ApiReference | null {
    try {
      const $ = cheerio.load(content);
      const title = $('title').text() || `${className} API Reference`;
      
      // 提取更详细的类描述
      const description = this.extractDetailedDescription($, classInfo);
      
      // 提取完整的方法信息
      const methods = this.extractDetailedMethods($, methodName);
      
      // 提取继承关系
      const inheritance = this.extractInheritanceInfo($);
      
      // 提取使用示例
      const examples = this.extractDetailedExamples($, className);
      
      return {
        className,
        methodName,
        namespace: classInfo.namespace,
        description,
        parameters: methods.length > 0 ? methods[0].parameters : [],
        returnType: methods.length > 0 ? methods[0].returnType : undefined,
        examples,
        relatedApis: [...classInfo.relatedClasses, ...inheritance.parents, ...inheritance.children],
        sourceFile: classInfo.headerFile,
        documentationUrl: url,
        inheritance: `父类: ${inheritance.parents.join(', ')}, 子类: ${inheritance.children.join(', ')}`
      };
      
    } catch (error) {
      console.log('⚠️ 解析增强 API页面失败:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }
  
  /**
   * 提取详细描述
   */
  private extractDetailedDescription($: cheerio.CheerioAPI, classInfo: any): string {
    const descriptions = [];
    
    // 基础描述
    descriptions.push(classInfo.description);
    
    // 从页面提取描述
    const briefDesc = $('.brief, .class-description, .overview').first().text().trim();
    if (briefDesc && briefDesc.length > classInfo.description.length) {
      descriptions.push(briefDesc);
    }
    
    // 提取使用场景
    const usageInfo = this.extractUsageScenarios($);
    if (usageInfo) {
      descriptions.push(`使用场景: ${usageInfo}`);
    }
    
    return descriptions.join('\n\n');
  }
  
  /**
   * 提取使用场景
   */
  private extractUsageScenarios($: cheerio.CheerioAPI): string {
    const usageKeywords = ['used for', 'suitable for', 'best for', 'commonly used'];
    let usage = '';
    
    $('p, .description, .note').each((_, element) => {
      const text = $(element).text().toLowerCase();
      usageKeywords.forEach(keyword => {
        if (text.includes(keyword)) {
          usage = $(element).text().trim();
          return false;
        }
      });
      if (usage) return false;
    });
    
    return usage;
  }
  
  /**
   * 提取详细方法信息
   */
  private extractDetailedMethods($: cheerio.CheerioAPI, targetMethod?: string): Array<{
    name: string;
    parameters: ApiParameter[];
    returnType: string;
    description: string;
    isStatic: boolean;
    isVirtual: boolean;
    visibility: 'public' | 'protected' | 'private';
    since?: string;
  }> {
    const methods: Array<{
      name: string;
      parameters: ApiParameter[];
      returnType: string;
      description: string;
      isStatic: boolean;
      isVirtual: boolean;
      visibility: 'public' | 'protected' | 'private';
      since?: string;
    }> = [];
    
    // 查找方法定义
    $('.method, .function, .member-function').each((_, element) => {
      const $element = $(element);
      const methodText = $element.text();
      
      if (targetMethod && !methodText.toLowerCase().includes(targetMethod.toLowerCase())) {
        return;
      }
      
      const methodInfo = this.parseDetailedMethodSignature($element);
      if (methodInfo) {
        methods.push(methodInfo);
      }
    });
    
    // 如果没有找到特定方法，返回前几个方法
    return targetMethod ? methods : methods.slice(0, 5);
  }
  
  /**
   * 解析详细方法签名
   */
  private parseDetailedMethodSignature($element: cheerio.Cheerio<any>): {
    name: string;
    parameters: ApiParameter[];
    returnType: string;
    description: string;
    isStatic: boolean;
    isVirtual: boolean;
    visibility: 'public' | 'protected' | 'private';
    since?: string;
  } | null {
    try {
      const signature = $element.find('.signature, .method-signature').text();
      const description = $element.find('.description, .brief').text().trim();
      
      // 简化的解析逻辑
      const match = signature.match(/(\w+)\s+(\w+)\s*\(([^)]*)\)/);
      if (!match) return null;
      
      const [, returnType, name, paramStr] = match;
      const parameters = this.parseDetailedParameters(paramStr);
      
      return {
        name,
        parameters,
        returnType,
        description: description || `${name} 方法`,
        isStatic: signature.includes('static'),
        isVirtual: signature.includes('virtual'),
        visibility: signature.includes('private') ? 'private' : 
                   signature.includes('protected') ? 'protected' : 'public',
        since: this.extractVersionFromElement($element)
      };
      
    } catch (error) {
      return null;
    }
  }
  
  /**
   * 解析详细参数
   */
  private parseDetailedParameters(paramStr: string): ApiParameter[] {
    if (!paramStr.trim()) return [];
    
    const parameters: ApiParameter[] = [];
    const params = paramStr.split(',');
    
    params.forEach(param => {
      const trimmed = param.trim();
      if (trimmed) {
        const parts = trimmed.split(/\s+/);
        if (parts.length >= 2) {
          const type = parts.slice(0, -1).join(' ');
          const name = parts[parts.length - 1].replace(/[=].*/, ''); // 移除默认值
          
          parameters.push({
            name,
            type,
            description: `${name} 参数`,
            optional: trimmed.includes('=') || trimmed.includes('nullptr') || trimmed.includes('default'),
            defaultValue: this.extractDefaultValue(trimmed)
          });
        }
      }
    });
    
    return parameters;
  }
  
  /**
   * 提取默认值
   */
  private extractDefaultValue(paramStr: string): string | undefined {
    const match = paramStr.match(/=\s*([^,)]+)/);
    return match ? match[1].trim() : undefined;
  }
  
  /**
   * 提取继承信息
   */
  private extractInheritanceInfo($: cheerio.CheerioAPI): {
    parents: string[];
    children: string[];
  } {
    const parents: string[] = [];
    const children: string[] = [];
    
    // 查找父类
    $('.inheritance, .base-classes').find('a').each((_, element) => {
      const className = $(element).text().trim();
      if (className) parents.push(className);
    });
    
    // 查找子类
    $('.derived-classes, .subclasses').find('a').each((_, element) => {
      const className = $(element).text().trim();
      if (className) children.push(className);
    });
    
    return { parents, children };
  }
  
  /**
   * 提取详细示例
   */
  private extractDetailedExamples($: cheerio.CheerioAPI, className: string): CodeExample[] {
    const examples: CodeExample[] = [];
    
    // 查找代码示例
    $('pre, .code-example, .example').each((_, element) => {
      const code = $(element).text().trim();
      if (code.length > 50 && code.includes(className)) {
        examples.push({
          title: `${className} 使用示例`,
          language: 'cpp',
          code,
          description: `从官方文档提取的 ${className} 使用示例`
        });
      }
    });
    
    // 如果没有找到示例，生成基础示例
    if (examples.length === 0) {
      const basicExample = this.generateBasicExample(className);
      if (basicExample) {
        examples.push(basicExample);
      }
    }
    
    return examples.slice(0, 3);
  }
  
  /**
   * 生成基础示例
   */
  private generateBasicExample(className: string): CodeExample | null {
    const classInfo = this.enhancedClassMappings[className.toLowerCase()];
    if (!classInfo) return null;
    
    let code = '';
    let description = '';
    
    switch (classInfo.category) {
      case '2d':
        if (className.toLowerCase().includes('sprite')) {
          code = `// 创建和使用 ${className}\nauto ${className.toLowerCase()} = ${className}::create("image.png");\n${className.toLowerCase()}->setPosition(Vec2(400, 300));\nthis->addChild(${className.toLowerCase()});`;
          description = `${className} 的基本创建和使用方法`;
        }
        break;
      case 'base':
        if (className.toLowerCase().includes('director')) {
          code = `// 获取和使用 ${className}\nauto director = ${className}::getInstance();\nauto scene = Scene::create();\ndirector->replaceScene(scene);`;
          description = `${className} 的基本获取和使用方法`;
        }
        break;
    }
    
    return code ? {
      title: `${className} 基础示例`,
      language: 'cpp',
      code,
      description
    } : null;
  }
  
  /**
   * 提取版本信息
   */
  private extractVersionInfo($: cheerio.CheerioAPI): string | undefined {
    const versionText = $('.version, .since, .added-in').text();
    const match = versionText.match(/(\d+\.\d+(\.\d+)?)/);
    return match ? match[1] : undefined;
  }
  
  /**
   * 从元素提取版本信息
   */
  private extractVersionFromElement($element: cheerio.Cheerio<any>): string | undefined {
    const versionText = $element.find('.version, .since').text();
    const match = versionText.match(/(\d+\.\d+(\.\d+)?)/);
    return match ? match[1] : undefined;
  }
  
  /**
   * 从官方文档获取API参考 - 原始方法保留
   */
  private async getOfficialApiReference(className: string, methodName?: string): Promise<ApiReference | null> {
    try {
      // 构建可能的API页面URL
      const possibleUrls = this.generateApiUrls(className);
      
      for (const url of possibleUrls) {
        try {
          const response = await networkUtils.get(url, {
            timeout: 10000,
            validateStatus: (status) => status === 200
          });

          const $ = cheerio.load(response.data);
          const apiRef = this.parseOfficialApiPage($, className, methodName, url);
          
          if (apiRef) {
            console.log(`✅ 从官方文档获取API: ${className}`);
            return apiRef;
          }

        } catch (error) {
          // 继续尝试下一个URL
          continue;
        }
      }

    } catch (error) {
      console.log('⚠️ 官方API文档获取失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 生成可能的API页面URL
   */
  private generateApiUrls(className: string): string[] {
    const urls: string[] = [];
    const classNameLower = className.toLowerCase();
    
    // 常见的类名到URL的映射模式
    const urlPatterns = [
      `classax_1_1_${classNameLower}.html`,
      `classax_1_1${classNameLower}.html`,
      `class_${classNameLower}.html`,
      `${classNameLower}.html`
    ];

    urlPatterns.forEach(pattern => {
      urls.push(`${this.OFFICIAL_DOCS_BASE}/${pattern}`);
    });

    return urls;
  }

  /**
   * 解析官方API页面
   */
  private parseOfficialApiPage(
    $: cheerio.CheerioAPI,
    className: string,
    methodName: string | undefined,
    url: string
  ): ApiReference | null {
    try {
      const title = $('title').text() || $('h1').first().text();
      
      if (!title.toLowerCase().includes(className.toLowerCase())) {
        return null;
      }

      // 提取类描述
      const description = $('.brief, .description, .detailed-description').first().text().trim() ||
                         $('p').first().text().trim() ||
                         '暂无描述';

      // 提取方法信息
      const methods = this.extractMethods($, methodName);
      
      // 提取相关API
      const relatedApis = this.extractRelatedApis($);

      // 检测命名空间
      const namespace = this.detectNamespace($, className);

      return {
        className,
        methodName,
        namespace,
        description,
        parameters: methods.length > 0 ? methods[0].parameters : [],
        returnType: methods.length > 0 ? methods[0].returnType : undefined,
        examples: this.extractExamples($),
        relatedApis,
        sourceFile: this.detectSourceFile($),
        documentationUrl: url
      };

    } catch (error) {
      console.log('⚠️ 解析API页面失败:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * 提取方法信息
   */
  private extractMethods($: cheerio.CheerioAPI, targetMethod?: string): Array<{
    name: string;
    parameters: ApiParameter[];
    returnType: string;
    description: string;
  }> {
    const methods: Array<{
      name: string;
      parameters: ApiParameter[];
      returnType: string;
      description: string;
    }> = [];

    // 查找方法定义
    $('.method, .function, .member').each((_, element) => {
      const $element = $(element);
      const methodText = $element.text();
      
      if (targetMethod && !methodText.toLowerCase().includes(targetMethod.toLowerCase())) {
        return;
      }

      const methodInfo = this.parseMethodSignature(methodText);
      if (methodInfo) {
        methods.push(methodInfo);
      }
    });

    return methods;
  }

  /**
   * 解析方法签名
   */
  private parseMethodSignature(signature: string): {
    name: string;
    parameters: ApiParameter[];
    returnType: string;
    description: string;
  } | null {
    try {
      // 简化的方法签名解析
      const match = signature.match(/(\w+)\s+(\w+)\s*\(([^)]*)\)/);
      if (!match) return null;

      const [, returnType, name, paramStr] = match;
      const parameters = this.parseParameters(paramStr);

      return {
        name,
        parameters,
        returnType,
        description: '从API文档提取的方法'
      };

    } catch (error) {
      return null;
    }
  }

  /**
   * 解析参数
   */
  private parseParameters(paramStr: string): ApiParameter[] {
    if (!paramStr.trim()) return [];

    const parameters: ApiParameter[] = [];
    const params = paramStr.split(',');

    params.forEach(param => {
      const trimmed = param.trim();
      if (trimmed) {
        const parts = trimmed.split(/\s+/);
        if (parts.length >= 2) {
          parameters.push({
            name: parts[parts.length - 1],
            type: parts.slice(0, -1).join(' '),
            description: '参数描述',
            optional: trimmed.includes('=') || trimmed.includes('default')
          });
        }
      }
    });

    return parameters;
  }

  /**
   * 提取相关API
   */
  private extractRelatedApis($: cheerio.CheerioAPI): string[] {
    const relatedApis: string[] = [];
    
    $('.related, .see-also, .inheritance').find('a').each((_, element) => {
      const text = $(element).text().trim();
      if (text && text.length > 0) {
        relatedApis.push(text);
      }
    });

    return [...new Set(relatedApis)].slice(0, 10);
  }

  /**
   * 提取示例代码
   */
  private extractExamples($: cheerio.CheerioAPI): CodeExample[] {
    const examples: CodeExample[] = [];
    
    $('pre, code').each((_, element) => {
      const code = $(element).text().trim();
      if (code.length > 50) { // 过滤掉太短的代码片段
        examples.push({
          title: 'API使用示例',
          language: 'cpp',
          code,
          description: '从API文档提取的示例代码'
        });
      }
    });

    return examples.slice(0, 3);
  }

  /**
   * 检测命名空间
   */
  private detectNamespace($: cheerio.CheerioAPI, className: string): string {
    const text = $('body').text();
    
    // 查找命名空间声明
    const namespaceMatch = text.match(/namespace\s+(\w+)/i);
    if (namespaceMatch) {
      return namespaceMatch[1];
    }

    // 默认为ax命名空间
    return 'ax';
  }

  /**
   * 检测源文件
   */
  private detectSourceFile($: cheerio.CheerioAPI): string {
    // 查找源文件信息
    const sourceInfo = $('.source-file, .file-info').text();
    if (sourceInfo) {
      const match = sourceInfo.match(/(\w+\.(h|hpp|cpp))/);
      if (match) {
        return match[1];
      }
    }

    return '未知';
  }

  /**
   * 从源码获取API参考
   */
  private async getSourceCodeApiReference(className: string, methodName?: string): Promise<ApiReference | null> {
    try {
      // 查找可能的头文件路径
      const possiblePaths = this.generateHeaderPaths(className);

      for (const path of possiblePaths) {
        try {
          const fileUrl = `${this.GITHUB_RAW_BASE}/${this.AXMOL_REPO}/dev/${path}`;
          const response = await networkUtils.get(fileUrl, { timeout: 8000 });

          const apiRef = this.parseSourceCodeApi(response.data, className, methodName, path);
          if (apiRef) {
            console.log(`✅ 从源码获取API: ${className}`);
            return apiRef;
          }

        } catch (error) {
          continue;
        }
      }

    } catch (error) {
      console.log('⚠️ 源码API获取失败:', error instanceof Error ? error.message : String(error));
    }

    return null;
  }

  /**
   * 生成可能的头文件路径
   */
  private generateHeaderPaths(className: string): string[] {
    const paths: string[] = [];
    const classNameVariations = [className, className.toLowerCase()];

    const basePaths = [
      'core/2d',
      'core/3d',
      'core/base',
      'core/ui',
      'core/audio',
      'core/physics',
      'core/renderer'
    ];

    basePaths.forEach(basePath => {
      classNameVariations.forEach(name => {
        paths.push(`${basePath}/${name}.h`);
        paths.push(`${basePath}/${name}.hpp`);
      });
    });

    return paths;
  }

  /**
   * 解析源码API
   */
  private parseSourceCodeApi(
    content: string,
    className: string,
    methodName: string | undefined,
    filePath: string
  ): ApiReference | null {
    try {
      // 查找类定义
      const classMatch = content.match(new RegExp(`class\\s+${className}[^{]*{([^}]*)}`, 'i'));
      if (!classMatch) return null;

      const classContent = classMatch[1];

      // 提取方法
      const methods = this.extractMethodsFromSource(classContent, methodName);

      return {
        className,
        methodName,
        namespace: 'ax',
        description: `${className} 类的API参考（从源码提取）`,
        parameters: methods.length > 0 ? methods[0].parameters : [],
        returnType: methods.length > 0 ? methods[0].returnType : undefined,
        examples: [],
        relatedApis: [],
        sourceFile: filePath,
        documentationUrl: `https://github.com/${this.AXMOL_REPO}/blob/dev/${filePath}`
      };

    } catch (error) {
      return null;
    }
  }

  /**
   * 从源码提取方法
   */
  private extractMethodsFromSource(content: string, targetMethod?: string): Array<{
    name: string;
    parameters: ApiParameter[];
    returnType: string;
    description: string;
  }> {
    const methods: Array<{
      name: string;
      parameters: ApiParameter[];
      returnType: string;
      description: string;
    }> = [];

    // 简化的方法提取逻辑
    const methodRegex = /(\w+)\s+(\w+)\s*\(([^)]*)\)\s*[;{]/g;
    let match;

    while ((match = methodRegex.exec(content)) !== null) {
      const [, returnType, name, paramStr] = match;

      if (targetMethod && !name.toLowerCase().includes(targetMethod.toLowerCase())) {
        continue;
      }

      methods.push({
        name,
        parameters: this.parseParameters(paramStr),
        returnType,
        description: `${name} 方法（从源码提取）`
      });
    }

    return methods;
  }

  /**
   * 查找相关类
   */
  private findRelatedClasses(className: string): string[] {
    const classNameLower = className.toLowerCase();
    const relatedClasses: string[] = [];

    // 精灵动画相关类的映射
    const classRelations: { [key: string]: string[] } = {
      'sprite': ['SpriteFrame', 'SpriteBatchNode', 'Texture2D', 'Animation', 'Animate'],
      'spriteframe': ['Sprite', 'SpriteFrameCache', 'Texture2D', 'Animation'],
      'animation': ['Sprite', 'SpriteFrame', 'Animate', 'Action', 'ActionInterval'],
      'animate': ['Animation', 'Sprite', 'SpriteFrame', 'Action'],
      'texture': ['Sprite', 'SpriteFrame', 'TextureCache', 'Image'],
      'action': ['Animation', 'Animate', 'ActionManager', 'Node'],
      'node': ['Sprite', 'Scene', 'Layer', 'Director'],
      'scene': ['Node', 'Director', 'Layer'],
      'director': ['Scene', 'Node', 'Scheduler']
    };

    // 查找直接相关的类
    if (classRelations[classNameLower]) {
      relatedClasses.push(...classRelations[classNameLower]);
    }

    // 查找部分匹配的类
    Object.entries(classRelations).forEach(([key, classes]) => {
      if (classNameLower.includes(key) || key.includes(classNameLower)) {
        relatedClasses.push(...classes);
      }
    });

    // 去重并限制数量
    return [...new Set(relatedClasses)].slice(0, 3);
  }

  /**
   * 生成基础API参考
   */
  private async generateBasicApiReference(className: string, methodName?: string): Promise<ApiReference | null> {
    const relatedApis = this.findRelatedClasses(className);

    // 基于类名生成更详细的API信息
    let description = `${className} 是 Axmol 引擎中的一个类。`;

    if (className.toLowerCase().includes('sprite')) {
      description += '这是一个与精灵渲染相关的类，用于显示2D图像和纹理。';
    } else if (className.toLowerCase().includes('animation')) {
      description += '这是一个与动画相关的类，用于创建和控制动画效果。';
    } else if (className.toLowerCase().includes('frame')) {
      description += '这是一个与帧相关的类，通常用于精灵动画中的单个帧。';
    }

    return {
      className,
      methodName,
      namespace: 'ax',
      description,
      parameters: [],
      examples: this.generateBasicExamples(className),
      relatedApis,
      sourceFile: '未知',
      documentationUrl: this.OFFICIAL_DOCS_BASE
    };
  }

  /**
   * 生成基础示例
   */
  private generateBasicExamples(className: string): CodeExample[] {
    const examples: CodeExample[] = [];
    const classNameLower = className.toLowerCase();

    if (classNameLower.includes('sprite')) {
      examples.push({
        title: 'Sprite 基础使用示例',
        language: 'cpp',
        code: `// 创建精灵
auto sprite = Sprite::create("image.png");
sprite->setPosition(Vec2(400, 300));
this->addChild(sprite);

// 使用精灵帧
auto frame = SpriteFrameCache::getInstance()->getSpriteFrameByName("frame.png");
auto sprite2 = Sprite::createWithSpriteFrame(frame);`,
        description: '基础的精灵创建和使用示例'
      });
    }

    if (classNameLower.includes('animation')) {
      examples.push({
        title: 'Animation 基础使用示例',
        language: 'cpp',
        code: `// 创建动画
Vector<SpriteFrame*> frames;
for (int i = 1; i <= 8; i++) {
    auto frame = SpriteFrameCache::getInstance()->getSpriteFrameByName(
        StringUtils::format("frame_%02d.png", i));
    frames.pushBack(frame);
}

auto animation = Animation::createWithSpriteFrames(frames, 0.1f);
auto animate = Animate::create(animation);
sprite->runAction(RepeatForever::create(animate));`,
        description: '基础的动画创建和播放示例'
      });
    }

    return examples;
  }

  /**
   * 增强的源码API参考获取
   */
  private async getEnhancedSourceCodeApiReference(className: string, methodName?: string): Promise<ApiReference | null> {
    try {
      const classInfo = this.enhancedClassMappings[className.toLowerCase()];
      if (!classInfo) {
        return this.getSourceCodeApiReference(className, methodName);
      }
      
      // 使用映射信息直接查找头文件
      const headerPath = classInfo.headerFile;
      const fileUrl = `${this.GITHUB_RAW_BASE}/${this.AXMOL_REPO}/dev/${headerPath}`;
      
      const response = await networkUtils.get(fileUrl, { timeout: 8000 });
      const apiRef = this.parseEnhancedSourceCode(response.data, className, methodName, headerPath, classInfo);
      
      if (apiRef) {
        console.log(`✅ 从增强源码获取API: ${className}`);
        return apiRef;
      }
      
      // 备用：原始方法
      return this.getSourceCodeApiReference(className, methodName);
      
    } catch (error) {
      console.log('⚠️ 增强源码API获取失败:', error instanceof Error ? error.message : String(error));
      return this.getSourceCodeApiReference(className, methodName);
    }
  }
  
  /**
   * 解析增强的源码
   */
  private parseEnhancedSourceCode(
    content: string,
    className: string,
    methodName: string | undefined,
    filePath: string,
    classInfo: any
  ): ApiReference | null {
    try {
      // 查找类定义
      const classRegex = new RegExp(`class\\s+${className}[^{]*{([^}]*)}`, 'i');
      const classMatch = content.match(classRegex);
      
      if (!classMatch) return null;
      
      const classContent = classMatch[1];
      
      // 提取详细方法信息
      const methods = this.extractMethodsFromEnhancedSource(classContent, methodName);
      
      // 提取类级别的注释
      const classComment = this.extractClassComment(content, className);
      
      return {
        className,
        methodName,
        namespace: classInfo.namespace,
        description: classComment || classInfo.description,
        parameters: methods.length > 0 ? methods[0].parameters : [],
        returnType: methods.length > 0 ? methods[0].returnType : undefined,
        examples: this.generateExamplesFromSource(className, methods),
        relatedApis: classInfo.relatedClasses,
        sourceFile: filePath,
        documentationUrl: `https://github.com/${this.AXMOL_REPO}/blob/dev/${filePath}`
      };
      
    } catch (error) {
      return null;
    }
  }
  
  /**
   * 从增强源码提取方法
   */
  private extractMethodsFromEnhancedSource(content: string, targetMethod?: string): Array<{
    name: string;
    parameters: ApiParameter[];
    returnType: string;
    description: string;
  }> {
    const methods: Array<{
      name: string;
      parameters: ApiParameter[];
      returnType: string;
      description: string;
    }> = [];
    
    // 增强的方法匹配正则
    const methodRegex = /(?:\/\*[^]*?\*\/\s*)?(?:static\s+)?(?:virtual\s+)?(\w+(?:<[^>]*>)?(?:\s*\*|\s*&)?)\s+(\w+)\s*\(([^)]*)\)\s*(?:const)?\s*[;{]/g;
    let match;
    
    while ((match = methodRegex.exec(content)) !== null) {
      const [fullMatch, returnType, name, paramStr] = match;
      
      if (targetMethod && !name.toLowerCase().includes(targetMethod.toLowerCase())) {
        continue;
      }
      
      // 提取方法注释
      const commentMatch = fullMatch.match(/\/\*[^]*?\*\//);
      const description = commentMatch ? 
        this.cleanComment(commentMatch[0]) : 
        `${name} 方法（从源码提取）`;
      
      methods.push({
        name,
        parameters: this.parseParameters(paramStr),
        returnType: returnType.trim(),
        description
      });
    }
    
    return methods;
  }
  
  /**
   * 清理注释
   */
  private cleanComment(comment: string): string {
    return comment
      .replace(/\/\*\*?|\*\//g, '')
      .replace(/^\s*\*\s?/gm, '')
      .trim();
  }
  
  /**
   * 提取类注释
   */
  private extractClassComment(content: string, className: string): string | null {
    const classRegex = new RegExp(`\/\*\*[^]*?\*\/\s*class\s+${className}`, 'i');
    const match = content.match(classRegex);
    
    if (match) {
      const comment = match[0].match(/\/\*\*[^]*?\*\//);
      return comment ? this.cleanComment(comment[0]) : null;
    }
    
    return null;
  }
  
  /**
   * 从源码生成示例
   */
  private generateExamplesFromSource(className: string, methods: any[]): CodeExample[] {
    const examples: CodeExample[] = [];
    
    // 根据常见方法生成示例
    const createMethod = methods.find(m => m.name.toLowerCase().includes('create'));
    if (createMethod) {
      const params = createMethod.parameters.length > 0 ? 
        createMethod.parameters.map((p: any) => `/* ${p.name} */`).join(', ') : '';
      
      examples.push({
        title: `${className} 创建示例`,
        language: 'cpp',
        code: `// 创建 ${className} 实例\nauto instance = ${className}::${createMethod.name}(${params});`,
        description: `使用 ${createMethod.name} 方法创建 ${className} 实例`
      });
    }
    
    return examples;
  }
  
  /**
   * 搜索相关类API
   */
  private async searchRelatedClassesApi(className: string, methodName?: string): Promise<ApiReference | null> {
    const classInfo = this.enhancedClassMappings[className.toLowerCase()];
    if (!classInfo) {
      return this.searchOriginalRelatedClasses(className, methodName);
    }
    
    // 使用增强的相关类信息
    for (const relatedClass of classInfo.relatedClasses) {
      const apiRef = await this.getEnhancedOfficialApiReference(relatedClass, methodName);
      if (apiRef) {
        apiRef.className = className; // 保持原始类名
        apiRef.description = `${className} 相关API参考（通过 ${relatedClass} 获取）\n\n${apiRef.description}`;
        return apiRef;
      }
    }
    
    // 备用：原始方法
    return this.searchOriginalRelatedClasses(className, methodName);
  }
  
  /**
   * 原始相关类搜索方法
   */
  private async searchOriginalRelatedClasses(className: string, methodName?: string): Promise<ApiReference | null> {
    const relatedClasses = this.findRelatedClasses(className);
    for (const relatedClass of relatedClasses) {
      const apiRef = await this.getOfficialApiReference(relatedClass, methodName);
      if (apiRef) {
        apiRef.className = className;
        apiRef.description = `${className} 相关API参考（通过 ${relatedClass} 获取）`;
        return apiRef;
      }
    }
    return null;
  }
  
  /**
   * 生成增强的API参考
   */
  private async generateEnhancedApiReference(className: string, methodName?: string): Promise<ApiReference | null> {
    const classInfo = this.enhancedClassMappings[className.toLowerCase()];
    
    if (classInfo) {
      return {
        className,
        methodName,
        namespace: classInfo.namespace,
        description: this.generateDetailedDescription(className, classInfo),
        parameters: [],
        examples: this.generateEnhancedExamples(className, classInfo),
        relatedApis: classInfo.relatedClasses,
        sourceFile: classInfo.headerFile,
        documentationUrl: this.OFFICIAL_DOCS_BASE
      };
    }
    
    // 备用：原始方法
    return this.generateBasicApiReference(className, methodName);
  }
  
  /**
   * 生成详细描述
   */
  private generateDetailedDescription(className: string, classInfo: any): string {
    const descriptions = [
      classInfo.description,
      `\n**分类**: ${classInfo.category}`,
      `**命名空间**: ${classInfo.namespace}`,
      `**头文件**: ${classInfo.headerFile}`
    ];
    
    // 根据类别添加特定信息
    switch (classInfo.category) {
      case '2d':
        descriptions.push('\n**特性**: 2D渲染和显示相关功能');
        break;
      case '3d':
        descriptions.push('\n**特性**: 3D渲染和模型相关功能');
        break;
      case 'base':
        descriptions.push('\n**特性**: 核心基础功能和框架');
        break;
    }
    
    return descriptions.join('');
  }
  
  /**
   * 生成增强示例
   */
  private generateEnhancedExamples(className: string, classInfo: any): CodeExample[] {
    const examples: CodeExample[] = [];
    
    // 基于类别生成更详细的示例
    switch (classInfo.category) {
      case '2d':
        if (className.toLowerCase().includes('sprite')) {
          examples.push({
            title: `${className} 完整使用示例`,
            language: 'cpp',
            code: this.generateSpriteExample(className),
            description: `${className} 的创建、配置和使用完整流程`
          });
        }
        break;
      case 'base':
        if (className.toLowerCase().includes('director')) {
          examples.push({
            title: `${className} 场景管理示例`,
            language: 'cpp',
            code: this.generateDirectorExample(className),
            description: `${className} 的场景管理和控制示例`
          });
        }
        break;
    }
    
    return examples;
  }
  
  /**
   * 生成Sprite示例
   */
  private generateSpriteExample(className: string): string {
    return `// ${className} 完整使用示例
// 1. 创建精灵
auto ${className.toLowerCase()} = ${className}::create("player.png");

// 2. 设置属性
${className.toLowerCase()}->setPosition(Vec2(400, 300));
${className.toLowerCase()}->setScale(1.5f);
${className.toLowerCase()}->setOpacity(200);

// 3. 添加到场景
this->addChild(${className.toLowerCase()});

// 4. 创建动画
auto animation = Animation::create();
for (int i = 1; i <= 4; i++) {
    auto frame = SpriteFrame::create(
        StringUtils::format("player_walk_%02d.png", i), 
        Rect(0, 0, 64, 64)
    );
    animation->addSpriteFrame(frame);
}
animation->setDelayPerUnit(0.1f);

auto animate = Animate::create(animation);
${className.toLowerCase()}->runAction(RepeatForever::create(animate));`;
  }
  
  /**
   * 生成Director示例
   */
  private generateDirectorExample(className: string): string {
    return `// ${className} 场景管理示例
// 1. 获取导演实例
auto director = ${className}::getInstance();

// 2. 创建新场景
auto newScene = Scene::create();

// 3. 场景切换
director->replaceScene(newScene);

// 4. 获取屏幕尺寸
auto visibleSize = director->getVisibleSize();
auto origin = director->getVisibleOrigin();

// 5. 暂停和恢复游戏
director->pause();
director->resume();

// 6. 设置帧率
director->setDisplayStats(true);
director->setAnimationInterval(1.0f / 60.0f);`;
  }
}

// 导出默认API参考服务实例
export const apiReferenceService = new ApiReferenceService();
