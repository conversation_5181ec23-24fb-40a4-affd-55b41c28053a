/**
 * 缓存键生成工具
 * 提供统一的缓存键生成和管理
 */

import crypto from 'crypto';

export class CacheKeyManager {
  private static readonly KEY_PREFIX = 'axmol_mcp';
  private static readonly MAX_KEY_LENGTH = 200;

  /**
   * 生成标准化的缓存键
   */
  static generateKey(
    service: string,
    operation: string,
    params: Record<string, any>,
    version: string = 'v1'
  ): string {
    // 构建基础键
    const baseKey = `${this.KEY_PREFIX}:${version}:${service}:${operation}`;
    
    // 处理参数
    const paramString = this.serializeParams(params);
    const fullKey = `${baseKey}:${paramString}`;
    
    // 如果键太长，使用哈希
    if (fullKey.length > this.MAX_KEY_LENGTH) {
      const hash = this.hashString(paramString);
      return `${baseKey}:${hash}`;
    }
    
    return fullKey;
  }

  /**
   * 生成服务特定的缓存键
   */
  static forService(service: string, params: Record<string, any>): string {
    return this.generateKey(service, 'default', params);
  }

  /**
   * 生成查询缓存键
   */
  static forQuery(service: string, query: string, options: Record<string, any> = {}): string {
    return this.generateKey(service, 'query', { query, ...options });
  }

  /**
   * 生成API缓存键
   */
  static forApi(service: string, endpoint: string, params: Record<string, any> = {}): string {
    return this.generateKey(service, 'api', { endpoint, ...params });
  }

  /**
   * 序列化参数为字符串
   */
  private static serializeParams(params: Record<string, any>): string {
    // 排序键以确保一致性
    const sortedKeys = Object.keys(params).sort();
    
    const parts = sortedKeys.map(key => {
      const value = params[key];
      if (value === null || value === undefined) {
        return `${key}=null`;
      }
      if (typeof value === 'object') {
        return `${key}=${JSON.stringify(value)}`;
      }
      return `${key}=${String(value)}`;
    });
    
    return parts.join('&');
  }

  /**
   * 字符串哈希
   */
  private static hashString(str: string): string {
    return crypto.createHash('md5').update(str).digest('hex').substring(0, 16);
  }

  /**
   * 清理键字符串
   */
  private static cleanKey(key: string): string {
    return key
      .replace(/[^a-zA-Z0-9:_-]/g, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();
  }

  /**
   * 验证缓存键格式
   */
  static validateKey(key: string): boolean {
    if (!key || key.length === 0) return false;
    if (key.length > this.MAX_KEY_LENGTH) return false;
    if (!key.startsWith(this.KEY_PREFIX)) return false;
    
    return /^[a-zA-Z0-9:_-]+$/.test(key);
  }

  /**
   * 解析缓存键
   */
  static parseKey(key: string): {
    prefix: string;
    version: string;
    service: string;
    operation: string;
    hash?: string;
  } | null {
    if (!this.validateKey(key)) return null;
    
    const parts = key.split(':');
    if (parts.length < 4) return null;
    
    return {
      prefix: parts[0],
      version: parts[1],
      service: parts[2],
      operation: parts[3],
      hash: parts[4]
    };
  }

  /**
   * 生成带时间戳的缓存键（用于临时缓存）
   */
  static withTimestamp(baseKey: string): string {
    const timestamp = Math.floor(Date.now() / 1000);
    return `${baseKey}:ts_${timestamp}`;
  }

  /**
   * 生成带用户ID的缓存键（如果需要用户特定缓存）
   */
  static withUser(baseKey: string, userId: string): string {
    const userHash = this.hashString(userId);
    return `${baseKey}:user_${userHash}`;
  }
}

// 导出便捷函数
export const cacheKey = {
  docs: (query: string, options: Record<string, any> = {}) =>
    CacheKeyManager.forQuery('documentation', query, options),
  
  api: (className: string, methodName?: string) =>
    CacheKeyManager.forApi('api_reference', 'class', { className, methodName }),
  
  code: (feature: string, platform: string, options: Record<string, any> = {}) =>
    CacheKeyManager.forQuery('code_examples', feature, { platform, ...options }),
  
  build: (platform: string, errorHash: string) =>
    CacheKeyManager.forApi('build_issue', 'solve', { platform, errorHash }),
  
  migration: (fromEngine: string, topic: string) =>
    CacheKeyManager.forApi('migration', 'guide', { fromEngine, topic }),
  
  platform: (platform: string, topic: string) =>
    CacheKeyManager.forApi('platform', 'info', { platform, topic }),
  
  analysis: (codeHash: string, language: string) =>
    CacheKeyManager.forApi('code_analysis', 'analyze', { codeHash, language }),
  
  version: (feature: string, versions: string[]) =>
    CacheKeyManager.forApi('version_compare', 'compare', { feature, versions: versions.sort() }),
  
  practices: (useCase: string) =>
    CacheKeyManager.forQuery('best_practices', useCase),
  
  community: (problemHash: string) =>
    CacheKeyManager.forApi('community', 'solutions', { problemHash })
};