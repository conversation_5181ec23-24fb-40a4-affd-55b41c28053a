/**
 * 测试相关性修复效果
 */

const { DocumentationService } = require('./dist/services/documentationService.js');

// 创建文档服务实例
const docService = new DocumentationService();

// 测试数据
const testContent = `
Axmol Engine API Documentation

Sprite Class
The Sprite class is used to display 2D images and textures in your game.
It supports sprite frames, animations, and batch rendering.

SpriteFrame Class
SpriteFrame represents a single frame in a sprite sheet or texture atlas.
It contains information about the texture region and can be used with Animation.

Animation Class
The Animation class is used to create frame-based animations for sprites.
It works with SpriteFrame objects to create smooth animated sequences.

Animate Action
The Animate action is used to play animations on sprites.
It takes an Animation object and applies it to a sprite over time.

Example Usage:
// Load sprite sheet
SpriteFrameCache::getInstance()->addSpriteFramesWithFile("character.plist");

// Create sprite with frame
auto sprite = Sprite::createWithSpriteFrameName("character_01.png");

// Create animation
Vector<SpriteFrame*> frames;
for (int i = 1; i <= 8; i++) {
    auto frame = SpriteFrameCache::getInstance()->getSpriteFrameByName(
        StringUtils::format("character_%02d.png", i));
    frames.pushBack(frame);
}

auto animation = Animation::createWithSpriteFrames(frames, 0.1f);
auto animate = Animate::create(animation);
sprite->runAction(RepeatForever::create(animate));
`;

// 测试查询
const testQueries = [
    "精灵图集 sprite sheet 角色动画 帧动画",
    "sprite animation",
    "SpriteFrame",
    "Animation",
    "texture atlas"
];

console.log("🧪 测试相关性计算修复效果\n");

// 测试每个查询的相关性分数
testQueries.forEach((query, index) => {
    console.log(`📝 测试查询 ${index + 1}: "${query}"`);
    
    // 使用反射调用私有方法进行测试
    try {
        // 提取搜索词汇
        const extractSearchTerms = docService.extractSearchTerms || function(query) {
            const terms = [];
            const spaceSplit = query.toLowerCase().split(/\s+/);
            terms.push(...spaceSplit);
            const englishWords = query.match(/[a-zA-Z]+/g);
            if (englishWords) {
                terms.push(...englishWords.map(w => w.toLowerCase()));
            }
            return [...new Set(terms)].filter(term => term.length >= 2);
        };
        
        // 计算相关性（模拟私有方法）
        const calculateRelevance = function(content, query) {
            const contentLower = content.toLowerCase();
            const queryTerms = extractSearchTerms(query);
            let score = 0;

            queryTerms.forEach(term => {
                if (term.length < 2) return;

                let termScore = 0;

                // 精确匹配
                const exactMatches = contentLower.match(new RegExp(term, 'gi'));
                if (exactMatches) {
                    termScore += exactMatches.length * 5;
                }

                // 部分匹配
                const partialTerm = term.substring(0, Math.max(3, term.length - 2));
                const partialMatches = contentLower.match(new RegExp(partialTerm, 'gi'));
                if (partialMatches && !exactMatches) {
                    termScore += partialMatches.length * 2;
                }

                // 游戏引擎相关术语
                const gameEngineScore = calculateGameEngineRelevance(contentLower, term);
                if (gameEngineScore > 0) {
                    termScore += gameEngineScore * 2;
                }

                // 精灵动画相关术语
                const spriteAnimationScore = calculateSpriteAnimationRelevance(contentLower, term);
                if (spriteAnimationScore > 0) {
                    termScore += spriteAnimationScore * 3;
                }

                score += termScore;
            });

            // 基础分数
            if (score === 0 && isGameEngineRelated(query)) {
                score = 3;
            }

            if (isSpriteAnimationRelated(query)) {
                score += 5;
            }

            return score;
        };

        // 辅助函数
        const calculateGameEngineRelevance = function(content, term) {
            const gameEngineTerms = {
                'sprite': ['node', 'texture', 'image', 'render', '2d', 'game', 'engine', 'spriteframe', 'spritebatchnode'],
                'animation': ['action', 'tween', 'frame', 'sequence', 'game', 'animate', 'animationframe'],
                'frame': ['sprite', 'animation', 'texture', 'sequence'],
                'sheet': ['sprite', 'texture', 'atlas', 'frame', 'plist'],
                '精灵': ['sprite', 'node', 'texture', 'image'],
                '动画': ['animation', 'animate', 'action', 'frame'],
                '图集': ['sheet', 'atlas', 'texture', 'sprite']
            };

            if (gameEngineTerms[term]) {
                let relatedScore = 0;
                gameEngineTerms[term].forEach(relatedTerm => {
                    if (content.includes(relatedTerm)) {
                        relatedScore += 1.5;
                    }
                });
                return relatedScore;
            }
            return 0;
        };

        const calculateSpriteAnimationRelevance = function(content, term) {
            const spriteAnimationTerms = {
                'sprite': ['spriteframe', 'spritebatchnode', 'animation', 'animate', 'texture', 'atlas'],
                'animation': ['spriteframe', 'animate', 'action', 'sequence', 'frame', 'duration'],
                'frame': ['spriteframe', 'animation', 'sequence', 'texture', 'atlas'],
                'sheet': ['spriteframe', 'texture', 'atlas', 'plist', 'animation'],
                '精灵': ['spriteframe', 'animation', 'texture'],
                '动画': ['spriteframe', 'animate', 'action', 'frame']
            };

            if (spriteAnimationTerms[term]) {
                let relatedScore = 0;
                spriteAnimationTerms[term].forEach(relatedTerm => {
                    if (content.includes(relatedTerm)) {
                        relatedScore += 2.0;
                    }
                });
                return relatedScore;
            }
            return 0;
        };

        const isGameEngineRelated = function(query) {
            const gameEngineKeywords = [
                'sprite', 'animation', 'scene', 'texture', 'sound', 'physics', 'ui',
                'camera', 'light', 'mesh', 'render', 'game', 'engine', '2d', '3d',
                'cocos', 'axmol', 'opengl', 'metal', 'shader', 'vertex', 'fragment',
                'frame', 'sheet', 'atlas', 'plist', '精灵', '动画', '图集', '帧'
            ];
            const queryLower = query.toLowerCase();
            return gameEngineKeywords.some(keyword => queryLower.includes(keyword));
        };

        const isSpriteAnimationRelated = function(query) {
            const spriteAnimationKeywords = [
                'sprite', 'animation', 'animate', 'frame', 'sheet', 'atlas', 'plist',
                'spriteframe', 'spritebatchnode', 'texture', 'sequence', 'duration',
                '精灵', '动画', '图集', '帧', '角色', '播放'
            ];
            const queryLower = query.toLowerCase();
            return spriteAnimationKeywords.some(keyword => queryLower.includes(keyword));
        };

        const relevanceScore = calculateRelevance(testContent, query);
        const searchTerms = extractSearchTerms(query);
        
        console.log(`   📊 相关性分数: ${relevanceScore}`);
        console.log(`   🔍 搜索词汇: ${searchTerms.join(', ')}`);
        console.log(`   ✅ 游戏引擎相关: ${isGameEngineRelated(query)}`);
        console.log(`   🎮 精灵动画相关: ${isSpriteAnimationRelated(query)}`);
        console.log('');
        
    } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
        console.log('');
    }
});

console.log("✅ 相关性测试完成");
