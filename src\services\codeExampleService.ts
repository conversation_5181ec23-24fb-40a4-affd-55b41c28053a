/**
 * 代码示例服务
 * 负责查找和提供 Axmol 相关的代码示例
 */

import * as cheerio from 'cheerio';
import { CodeExample, AxmolResource, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

export class CodeExampleService {
  private readonly GITHUB_API_BASE = 'https://api.github.com';
  private readonly GITHUB_RAW_BASE = 'https://raw.githubusercontent.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1小时缓存
  
  // 增强的示例源配置
  private readonly enhancedExampleSources = {
    official: {
      repo: 'axmolengine/axmol',
      paths: ['tests/cpp-tests', 'tests/lua-tests', 'templates', 'examples'],
      priority: 10,
      description: '官方示例和测试代码'
    },
    community: {
      repo: 'axmolengine/axmol-examples',
      paths: [''],
      priority: 8,
      description: '社区示例项目'
    },
    documentation: {
      repo: 'axmolengine/axmol',
      paths: ['docs/examples', 'docs/tutorials'],
      priority: 7,
      description: '文档中的示例代码'
    },
    samples: {
      repo: 'axmolengine/axmol-samples',
      paths: [''],
      priority: 6,
      description: '官方示例项目'
    }
  };
  
  // 增强的特征分析器
  private readonly advancedFeatureAnalyzer = {
    // 核心类别映射
    categories: {
      'graphics': ['sprite', 'texture', 'render', 'draw', 'opengl', 'shader', 'batch'],
      'animation': ['animate', 'action', 'sequence', 'spawn', 'ease', 'tween', 'interpolate'],
      'audio': ['audio', 'sound', 'music', 'effect', 'volume', 'stream'],
      'physics': ['physics', 'body', 'collision', 'joint', 'world', 'shape'],
      'input': ['touch', 'gesture', 'keyboard', 'mouse', 'accelerometer', 'gyroscope'],
      'ui': ['widget', 'button', 'label', 'textfield', 'layout', 'scrollview', 'listview'],
      'network': ['http', 'websocket', 'download', 'upload', 'socket'],
      'storage': ['userdefault', 'file', 'database', 'cache', 'persistent'],
      'scene': ['scene', 'layer', 'node', 'director', 'transition'],
      'platform': ['android', 'ios', 'windows', 'mac', 'linux', 'web']
    },
    // 复杂查询分析
    complexQueries: {
      'sprite animation': ['sprite', 'animation', 'spriteframe', 'animate', 'action'],
      'physics collision': ['physics', 'collision', 'body', 'contact', 'listener'],
      'ui layout': ['ui', 'layout', 'widget', 'size', 'position', 'anchor'],
      'touch input': ['touch', 'gesture', 'input', 'event', 'listener'],
      'audio effects': ['audio', 'effect', 'sound', 'music', 'volume']
    }
  };

  /**
   * 查找代码示例
   */
  async findCodeExamples(
    feature: string,
    platform: string = 'all',
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 开始搜索代码示例: "${feature}" (平台: ${platform})`);

      // 生成缓存键
      const cacheKey = `code_examples_${feature}_${platform}_${JSON.stringify(options)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as CodeExample[] | null;
        if (cached) {
          console.log('✅ 从缓存获取代码示例');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const results: CodeExample[] = [];
      const sources: string[] = [];

      // 智能搜索策略：根据查询类型选择最优数据源
      const searchStrategy = this.analyzeSearchStrategy(feature, options);
      
      // 并发搜索多个增强的数据源
      const searchPromises = [];
      
      // 1. 搜索官方示例（始终包含）
      searchPromises.push(
        this.searchEnhancedOfficialExamples(feature, platform, options)
          .then(examples => ({ source: 'official_examples', examples }))
      );
      
      // 2. 根据策略添加其他搜索
      if (searchStrategy.includeTests) {
        searchPromises.push(
          this.searchAdvancedTestCode(feature, platform, options)
            .then(examples => ({ source: 'test_code', examples }))
        );
      }
      
      if (searchStrategy.includeCommunity) {
        searchPromises.push(
          this.searchCommunityExamples(feature, platform, options)
            .then(examples => ({ source: 'community_examples', examples }))
        );
      }
      
      if (searchStrategy.includeSource) {
        searchPromises.push(
          this.searchEnhancedSourceExamples(feature, platform, options)
            .then(examples => ({ source: 'source_examples', examples }))
        );
      }
      
      // 3. 搜索文档示例
      searchPromises.push(
        this.searchDocumentationExamples(feature, platform, options)
          .then(examples => ({ source: 'documentation_examples', examples }))
      );
      
      // 并发执行所有搜索
      const searchResults = await Promise.allSettled(searchPromises);
      
      // 合并结果
      searchResults.forEach(result => {
        if (result.status === 'fulfilled' && result.value.examples.length > 0) {
          results.push(...result.value.examples);
          sources.push(result.value.source);
        }
      });

      // 高级质量评估和排序
      const sortedResults = this.advancedQualitySort(results, feature, searchStrategy);
      
      // 限制结果数量
      const maxResults = options.maxResults || 15;
      const finalResults = sortedResults.slice(0, maxResults);

      // 缓存结果
      if (options.useCache !== false && finalResults.length > 0) {
        await defaultCache.set(cacheKey, finalResults, this.CACHE_TTL);
      }

      console.log(`✅ 代码示例搜索完成: 找到 ${finalResults.length} 个示例`);

      return {
        success: true,
        data: finalResults,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: finalResults.length,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'findCodeExamples', { feature, platform, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }
  
  /**
   * 分析搜索策略
   */
  private analyzeSearchStrategy(feature: string, options: SearchOptions): {
    includeTests: boolean;
    includeCommunity: boolean;
    includeSource: boolean;
    priority: string[];
    complexity: 'simple' | 'moderate' | 'complex';
  } {
    const featureLower = feature.toLowerCase();
    const isComplexQuery = featureLower.split(' ').length > 2;
    
    // 检测查询类型
    let category = 'general';
    for (const [cat, keywords] of Object.entries(this.advancedFeatureAnalyzer.categories)) {
      if (keywords.some(keyword => featureLower.includes(keyword))) {
        category = cat;
        break;
      }
    }
    
    // 检测复杂查询
    const isComplexFeature = Object.keys(this.advancedFeatureAnalyzer.complexQueries)
      .some(complexQuery => featureLower.includes(complexQuery));
    
    return {
      includeTests: category === 'physics' || category === 'graphics' || isComplexFeature,
      includeCommunity: category === 'ui' || category === 'animation' || (options.maxResults ? options.maxResults > 10 : false),
      includeSource: category === 'graphics' || category === 'audio' || isComplexQuery,
      priority: this.getPriorityOrder(category),
      complexity: isComplexFeature ? 'complex' : isComplexQuery ? 'moderate' : 'simple'
    };
  }
  
  /**
   * 获取优先级顺序
   */
  private getPriorityOrder(category: string): string[] {
    const priorityMap: { [key: string]: string[] } = {
      'graphics': ['official_examples', 'test_code', 'source_examples', 'documentation_examples'],
      'animation': ['official_examples', 'community_examples', 'test_code', 'documentation_examples'],
      'ui': ['community_examples', 'official_examples', 'documentation_examples', 'test_code'],
      'physics': ['test_code', 'official_examples', 'source_examples', 'documentation_examples'],
      'audio': ['official_examples', 'source_examples', 'community_examples', 'test_code'],
      'default': ['official_examples', 'test_code', 'community_examples', 'source_examples']
    };
    
    return priorityMap[category] || priorityMap.default;
  }

  /**
   * 搜索官方示例代码
   */
  private async searchEnhancedOfficialExamples(
    feature: string,
    platform: string,
    options: SearchOptions
  ): Promise<CodeExample[]> {
    const results: CodeExample[] = [];
    const officialSource = this.enhancedExampleSources.official;
    
    try {
      // 智能路径选择：根据特征选择最相关的路径
      const relevantPaths = this.selectRelevantPaths(feature, officialSource.paths);
      
      console.log(`🔍 搜索官方示例: ${feature} 在 ${relevantPaths.length} 个路径中`);
      
      // 并发搜索多个路径但有超时控制
      const searchPromises = relevantPaths.map(path => 
        Promise.race([
          this.searchEnhancedExamplesInPath(officialSource.repo, path, feature, platform, options),
          new Promise<CodeExample[]>((_, reject) => 
            setTimeout(() => reject(new Error('Search timeout')), 8000)
          )
        ]).catch(error => {
          console.log(`⚠️ 路径 ${path} 搜索失败: ${error.message}`);
          return [] as CodeExample[];
        })
      );
      
      const searchResults = await Promise.allSettled(searchPromises);
      searchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(...result.value);
        }
      });
      
      console.log(`📚 官方示例搜索完成: ${results.length} 个示例`);
      
    } catch (error) {
      console.log('⚠️ 官方示例搜索失败:', error instanceof Error ? error.message : String(error));
    }
    
    return results;
  }

  /**
   * 选择相关路径
   */
  private selectRelevantPaths(feature: string, allPaths: string[]): string[] {
    const featureLower = feature.toLowerCase();
    const relevantPaths: { path: string; score: number }[] = [];
    
    for (const path of allPaths) {
      let score = 1; // 基础分数
      
      // 根据路径名称评分
      if (path.includes('tests') && (featureLower.includes('test') || featureLower.includes('example'))) {
        score += 3;
      }
      if (path.includes('lua') && featureLower.includes('lua')) {
        score += 2;
      }
      if (path.includes('cpp') && (featureLower.includes('cpp') || featureLower.includes('c++'))) {
        score += 2;
      }
      if (path.includes('template') && featureLower.includes('template')) {
        score += 2;
      }
      
      relevantPaths.push({ path, score });
    }
    
    // 按分数排序并返回前3个
    return relevantPaths
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(item => item.path);
  }

  /**
   * 增强的路径搜索
   */
  private async searchEnhancedExamplesInPath(
    repo: string,
    path: string,
    feature: string,
    platform: string,
    options: SearchOptions
  ): Promise<CodeExample[]> {
    const results: CodeExample[] = [];
    
    try {
      const contentsUrl = `${this.GITHUB_API_BASE}/repos/${repo}/contents/${path}`;
      
      const response = await networkUtils.get(contentsUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 10000
      });
      
      // 增强文件过滤：使用更智能的相关性判断
      const relevantFiles = response.data
        .filter((item: any) => item.type === 'file')
        .filter((file: any) => this.isEnhancedRelevantFile(file.name, feature, options.language))
        .sort((a: any, b: any) => this.calculateFileRelevance(b.name, feature) - this.calculateFileRelevance(a.name, feature))
        .slice(0, 5); // 增加文件数量但有限制
      
      // 并发处理文件但有超时控制
      const extractPromises = relevantFiles.map((file: any) => 
        Promise.race([
          this.extractEnhancedCodeExample(file, feature, platform),
          new Promise<null>((_, reject) => 
            setTimeout(() => reject(new Error('Extract timeout')), 4000)
          )
        ]).catch(() => null)
      );
      
      const extractResults = await Promise.allSettled(extractPromises);
      extractResults.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          results.push(result.value);
        }
      });
      
    } catch (error) {
      console.log(`⚠️ 搜索路径失败: ${path}`, error instanceof Error ? error.message : String(error));
    }
    
    return results;
  }

  /**
   * 增强的文件相关性判断
   */
  private isEnhancedRelevantFile(filename: string, feature: string, language?: string): boolean {
    const featureLower = feature.toLowerCase();
    const filenameLower = filename.toLowerCase();
    
    // 检查文件扩展名
    const validExtensions = language === 'lua' ? ['.lua'] : 
                           language === 'cpp' ? ['.cpp', '.h', '.cc', '.cxx', '.hpp'] :
                           ['.cpp', '.h', '.cc', '.cxx', '.hpp', '.lua', '.c'];
    
    const hasValidExtension = validExtensions.some(ext => filename.endsWith(ext));
    if (!hasValidExtension) return false;
    
    // 使用增强的特征关键词匹配
    const enhancedKeywords = this.getEnhancedFeatureKeywords(feature);
    const relevanceScore = this.calculateFileRelevance(filename, feature);
    
    return relevanceScore > 0;
  }
  
  /**
   * 计算文件相关性分数
   */
  private calculateFileRelevance(filename: string, feature: string): number {
    const filenameLower = filename.toLowerCase();
    const enhancedKeywords = this.getEnhancedFeatureKeywords(feature);
    let score = 0;
    
    // 直接匹配（最高分）
    enhancedKeywords.forEach(keyword => {
      if (filenameLower.includes(keyword.toLowerCase())) {
        score += keyword === feature.toLowerCase() ? 10 : 5;
      }
    });
    
    // 部分匹配
    enhancedKeywords.forEach(keyword => {
      if (keyword.length > 3) {
        const partialMatch = keyword.substring(0, Math.min(keyword.length - 1, 6));
        if (filenameLower.includes(partialMatch)) {
          score += 2;
        }
      }
    });
    
    // 文件名特殊加分
    if (filenameLower.includes('test') && feature.toLowerCase().includes('test')) score += 3;
    if (filenameLower.includes('example') || filenameLower.includes('demo')) score += 3;
    if (filenameLower.includes('sample')) score += 2;
    
    return score;
  }
  
  /**
   * 获取增强的特征关键词
   */
  private getEnhancedFeatureKeywords(feature: string): string[] {
    const keywords = [feature];
    const featureLower = feature.toLowerCase();
    
    // 使用高级特征分析器
    Object.entries(this.advancedFeatureAnalyzer.categories).forEach(([category, catKeywords]) => {
      if (catKeywords.some(keyword => featureLower.includes(keyword))) {
        keywords.push(...catKeywords);
      }
    });
    
    // 复杂查询分析
    Object.entries(this.advancedFeatureAnalyzer.complexQueries).forEach(([query, queryKeywords]) => {
      if (featureLower.includes(query.replace(' ', '')) || 
          query.split(' ').every(word => featureLower.includes(word))) {
        keywords.push(...queryKeywords);
      }
    });
    
    // 添加同义词和相关词
    const synonyms = this.getSynonyms(feature);
    keywords.push(...synonyms);
    
    return [...new Set(keywords)];
  }
  
  /**
   * 获取同义词
   */
  private getSynonyms(feature: string): string[] {
    const synonymMap: { [key: string]: string[] } = {
      'sprite': ['image', 'texture', 'graphic', 'picture', 'icon'],
      'animation': ['animate', 'motion', 'tween', 'interpolation', 'sequence'],
      'sound': ['audio', 'music', 'effect', 'wav', 'mp3'],
      'input': ['touch', 'gesture', 'event', 'interaction', 'control'],
      'ui': ['interface', 'widget', 'component', 'element', 'control'],
      'physics': ['collision', 'gravity', 'force', 'body', 'dynamics'],
      '精灵': ['sprite', 'image', 'texture'],
      '动画': ['animation', 'animate', 'motion'],
      '音频': ['audio', 'sound', 'music'],
      '输入': ['input', 'touch', 'event'],
      '界面': ['ui', 'interface', 'widget'],
      '物理': ['physics', 'collision', 'body']
    };
    
    const featureLower = feature.toLowerCase();
    const synonyms: string[] = [];
    
    Object.entries(synonymMap).forEach(([key, values]) => {
      if (featureLower.includes(key) || values.some(v => featureLower.includes(v))) {
        synonyms.push(...values);
      }
    });
    
    return synonyms;
  }

  /**
   * 搜索官方示例代码 - 原方法保留
   */
  private async searchOfficialExamples(
    feature: string,
    platform: string,
    options: SearchOptions
  ): Promise<CodeExample[]> {
    const results: CodeExample[] = [];

    // 减少搜索路径，优先搜索最相关的
    const examplePaths = ['tests/cpp-tests'];
    if (options.language === 'lua') {
      examplePaths.push('tests/lua-tests');
    }

    try {
      // 优化搜索策略：并发搜索多个路径但有超时控制
    const searchPromises = examplePaths.slice(0, 2).map(path =>
      Promise.race([
        this.searchExamplesInPath(path, feature, platform, options),
        new Promise<CodeExample[]>((_, reject) =>
          setTimeout(() => reject(new Error('Search timeout')), 6000) // 减少超时时间
        )
      ]).catch(error => {
        console.log(`⚠️ 路径 ${path} 搜索失败: ${error.message}`);
        return [] as CodeExample[];
      })
    );

    const searchResults = await Promise.allSettled(searchPromises);
    searchResults.forEach(result => {
      if (result.status === 'fulfilled') {
        results.push(...result.value);
      }
    });

      console.log(`📚 官方示例搜索完成: ${results.length} 个示例`);

    } catch (error) {
      console.log('⚠️ 官方示例搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return results;
  }

  /**
   * 在指定路径中搜索示例
   */
  private async searchExamplesInPath(
    path: string,
    feature: string,
    platform: string,
    options: SearchOptions
  ): Promise<CodeExample[]> {
    const results: CodeExample[] = [];

    try {
      const contentsUrl = `${this.GITHUB_API_BASE}/repos/${this.AXMOL_REPO}/contents/${path}`;
      
      const response = await networkUtils.get(contentsUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 10000
      });

      const files = response.data.filter((item: any) =>
        item.type === 'file' &&
        this.isRelevantFile(item.name, feature, options.language)
      );

      // 进一步限制文件数量并添加超时控制
      for (const file of files.slice(0, 3)) {
        try {
          const example = await Promise.race([
            this.extractCodeExample(file, feature, platform),
            new Promise<null>((_, reject) =>
              setTimeout(() => reject(new Error('Extract timeout')), 3000)
            )
          ]);

          if (example) {
            results.push(example);
          }
        } catch (error) {
          console.log(`⚠️ 提取代码示例超时或失败: ${file.name}`);
          continue;
        }
      }

    } catch (error) {
      console.log(`⚠️ 搜索路径失败: ${path}`, error instanceof Error ? error.message : String(error));
    }

    return results;
  }

  /**
   * 检查文件是否相关
   */
  private isRelevantFile(filename: string, feature: string, language?: string): boolean {
    const featureLower = feature.toLowerCase();
    const filenameLower = filename.toLowerCase();

    // 检查文件扩展名
    const validExtensions = language === 'lua' ? ['.lua'] : 
                           language === 'cpp' ? ['.cpp', '.h', '.cc', '.cxx'] :
                           ['.cpp', '.h', '.cc', '.cxx', '.lua'];
    
    const hasValidExtension = validExtensions.some(ext => filename.endsWith(ext));
    if (!hasValidExtension) return false;

    // 检查文件名是否包含特征词
    const featureKeywords = this.extractFeatureKeywords(feature);
    return featureKeywords.some(keyword => filenameLower.includes(keyword.toLowerCase()));
  }

  /**
   * 提取特征关键词 - 原方法保留兼容
   */
  private extractFeatureKeywords(feature: string): string[] {
    return this.getEnhancedFeatureKeywords(feature);
  }

  /**
   * 增强的代码示例提取
   */
  private async extractEnhancedCodeExample(file: any, feature: string, platform: string): Promise<CodeExample | null> {
    try {
      const response = await networkUtils.get(file.download_url, { timeout: 10000 });
      const content = response.data;
      
      // 高级内容相关性分析
      const relevanceAnalysis = this.analyzeContentRelevance(content, feature);
      if (relevanceAnalysis.score < 2) {
        return null;
      }
      
      // 智能代码段提取
      const intelligentSegments = this.extractIntelligentCodeSegments(content, feature, relevanceAnalysis);
      if (intelligentSegments.length === 0) {
        return null;
      }
      
      const language = file.name.endsWith('.lua') ? 'lua' : 'cpp';
      
      return {
        title: this.generateEnhancedTitle(file.name, feature, relevanceAnalysis),
        language,
        code: this.formatCodeSegments(intelligentSegments, language),
        description: this.generateEnhancedDescription(file.name, feature, intelligentSegments, relevanceAnalysis),
        platform: this.detectAdvancedPlatform(content, platform),
        version: this.extractVersionInfo(content) || 'latest',
        sourceUrl: file.html_url
      };
      
    } catch (error) {
      console.log(`⚠️ 提取增强代码示例失败: ${file.name}`);
      return null;
    }
  }
  
  /**
   * 分析内容相关性
   */
  private analyzeContentRelevance(content: string, feature: string): {
    score: number;
    matchedKeywords: string[];
    contexts: string[];
    complexity: 'basic' | 'intermediate' | 'advanced';
  } {
    const contentLower = content.toLowerCase();
    const enhancedKeywords = this.getEnhancedFeatureKeywords(feature);
    const matchedKeywords: string[] = [];
    const contexts: string[] = [];
    let score = 0;
    
    // 关键词匹配评分
    enhancedKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      const matches = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length;
      if (matches > 0) {
        matchedKeywords.push(keyword);
        score += matches * (keyword === feature.toLowerCase() ? 3 : 1);
        
        // 提取上下文
        const lines = content.split('\n');
        lines.forEach((line, index) => {
          if (line.toLowerCase().includes(keywordLower)) {
            const start = Math.max(0, index - 2);
            const end = Math.min(lines.length, index + 3);
            contexts.push(lines.slice(start, end).join('\n'));
          }
        });
      }
    });
    
    // 复杂度评估
    let complexity: 'basic' | 'intermediate' | 'advanced' = 'basic';
    if (matchedKeywords.length > 3 && score > 10) complexity = 'advanced';
    else if (matchedKeywords.length > 1 && score > 5) complexity = 'intermediate';
    
    return {
      score,
      matchedKeywords: [...new Set(matchedKeywords)],
      contexts: [...new Set(contexts)].slice(0, 3),
      complexity
    };
  }
  
  /**
   * 智能代码段提取
   */
  private extractIntelligentCodeSegments(content: string, feature: string, relevanceAnalysis: any): Array<{
    code: string;
    type: 'function' | 'class' | 'example' | 'usage';
    relevance: number;
    startLine: number;
    endLine: number;
  }> {
    const lines = content.split('\n');
    const segments: any[] = [];
    
    // 查找函数定义
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // C++函数匹配
      if (line.match(/^\s*(static\s+)?(\w+\s+)*\w+\s*\([^)]*\)\s*\{?\s*$/)) {
        const funcName = line.match(/\w+(?=\s*\()/)?.[0] || '';
        if (this.isRelevantFunction(funcName, feature)) {
          const endLine = this.findFunctionEnd(lines, i);
          segments.push({
            code: lines.slice(i, endLine + 1).join('\n'),
            type: 'function',
            relevance: this.calculateSegmentRelevance(lines.slice(i, endLine + 1).join('\n'), feature),
            startLine: i,
            endLine
          });
          i = endLine;
        }
      }
      
      // 类定义匹配
      if (line.match(/^\s*class\s+\w+/)) {
        const className = line.match(/class\s+(\w+)/)?.[1] || '';
        if (this.isRelevantClass(className, feature)) {
          const endLine = this.findClassEnd(lines, i);
          segments.push({
            code: lines.slice(i, Math.min(endLine + 1, i + 50)).join('\n'), // 限制类长度
            type: 'class',
            relevance: this.calculateSegmentRelevance(lines.slice(i, endLine + 1).join('\n'), feature),
            startLine: i,
            endLine: Math.min(endLine, i + 50)
          });
          i = Math.min(endLine, i + 50);
        }
      }
      
      // 示例代码段（注释中包含example或demo）
      if (line.match(/\/\/.*\b(example|demo|usage)/i) || line.match(/\/\*.*\b(example|demo|usage)/i)) {
        const endLine = this.findExampleEnd(lines, i);
        segments.push({
          code: lines.slice(i, endLine + 1).join('\n'),
          type: 'example',
          relevance: 5, // 示例代码高优先级
          startLine: i,
          endLine
        });
        i = endLine;
      }
    }
    
    // 按相关性排序并限制数量
    return segments
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 5);
  }
  
  /**
   * 判断函数是否相关
   */
  private isRelevantFunction(funcName: string, feature: string): boolean {
    const enhancedKeywords = this.getEnhancedFeatureKeywords(feature);
    const funcNameLower = funcName.toLowerCase();
    
    return enhancedKeywords.some(keyword => 
      funcNameLower.includes(keyword.toLowerCase()) || keyword.toLowerCase().includes(funcNameLower)
    );
  }
  
  /**
   * 判断类是否相关
   */
  private isRelevantClass(className: string, feature: string): boolean {
    const enhancedKeywords = this.getEnhancedFeatureKeywords(feature);
    const classNameLower = className.toLowerCase();
    
    return enhancedKeywords.some(keyword => 
      classNameLower.includes(keyword.toLowerCase()) || keyword.toLowerCase().includes(classNameLower)
    );
  }
  
  /**
   * 查找函数结束
   */
  private findFunctionEnd(lines: string[], startLine: number): number {
    let braceCount = 0;
    let foundOpenBrace = false;
    
    for (let i = startLine; i < lines.length && i < startLine + 100; i++) {
      const line = lines[i];
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          foundOpenBrace = true;
        } else if (char === '}') {
          braceCount--;
          if (foundOpenBrace && braceCount === 0) {
            return i;
          }
        }
      }
    }
    
    return Math.min(startLine + 20, lines.length - 1);
  }
  
  /**
   * 查找类结束
   */
  private findClassEnd(lines: string[], startLine: number): number {
    let braceCount = 0;
    let foundOpenBrace = false;
    
    for (let i = startLine; i < lines.length && i < startLine + 200; i++) {
      const line = lines[i];
      for (const char of line) {
        if (char === '{') {
          braceCount++;
          foundOpenBrace = true;
        } else if (char === '}') {
          braceCount--;
          if (foundOpenBrace && braceCount === 0) {
            return i;
          }
        }
      }
    }
    
    return Math.min(startLine + 50, lines.length - 1);
  }
  
  /**
   * 查找示例结束
   */
  private findExampleEnd(lines: string[], startLine: number): number {
    // 查找下一个空行或新的注释块
    for (let i = startLine + 1; i < lines.length && i < startLine + 30; i++) {
      const line = lines[i].trim();
      if (line === '' || line.startsWith('//') || line.startsWith('/*')) {
        return i - 1;
      }
    }
    return Math.min(startLine + 20, lines.length - 1);
  }
  
  /**
   * 计算代码段相关性
   */
  private calculateSegmentRelevance(code: string, feature: string): number {
    const enhancedKeywords = this.getEnhancedFeatureKeywords(feature);
    const codeLower = code.toLowerCase();
    let relevance = 0;
    
    enhancedKeywords.forEach(keyword => {
      const matches = (codeLower.match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
      relevance += matches * (keyword === feature.toLowerCase() ? 3 : 1);
    });
    
    return relevance;
  }
  
  /**
   * 格式化代码段
   */
  private formatCodeSegments(segments: any[], language: string): string {
    if (segments.length === 0) return '';
    
    // 按类型和相关性排序
    const sortedSegments = segments.sort((a, b) => {
      const typeOrder = { 'example': 0, 'function': 1, 'class': 2, 'usage': 3 };
      const typeDiff = (typeOrder[a.type as keyof typeof typeOrder] || 4) - (typeOrder[b.type as keyof typeof typeOrder] || 4);
      return typeDiff !== 0 ? typeDiff : b.relevance - a.relevance;
    });
    
    return sortedSegments
      .map((segment, index) => {
        const header = `// ${segment.type.toUpperCase()} - Lines ${segment.startLine + 1}-${segment.endLine + 1}`;
        return `${header}\n${segment.code}`;
      })
      .join('\n\n// ========================================\n\n');
  }
  
  /**
   * 生成增强标题
   */
  private generateEnhancedTitle(filename: string, feature: string, relevanceAnalysis: any): string {
    const baseName = filename.replace(/\.(cpp|h|lua|c|hpp|cc|cxx)$/, '');
    const complexityBadge = {
      'basic': '🔵',
      'intermediate': '🟡', 
      'advanced': '🔴'
    }[relevanceAnalysis.complexity as 'basic' | 'intermediate' | 'advanced'] || '⚪';
    
    return `${complexityBadge} ${feature} 示例 - ${baseName} (${relevanceAnalysis.matchedKeywords.length}个匹配)`;
  }
  
  /**
   * 生成增强描述
   */
  private generateEnhancedDescription(filename: string, feature: string, segments: any[], relevanceAnalysis: any): string {
    const segmentTypes = [...new Set(segments.map(s => s.type))];
    const keywordsList = relevanceAnalysis.matchedKeywords.slice(0, 3).join(', ');
    
    return `从 ${filename} 中提取的 ${feature} 相关代码示例。\n` +
           `包含: ${segments.length} 个代码段 (${segmentTypes.join(', ')})\n` +
           `匹配关键词: ${keywordsList}\n` +
           `复杂度: ${relevanceAnalysis.complexity}`;
  }
  
  /**
   * 高级平台检测
   */
  private detectAdvancedPlatform(content: string, requestedPlatform: string): string {
    if (requestedPlatform !== 'all') return requestedPlatform;
    
    const contentLower = content.toLowerCase();
    const platformIndicators = {
      'android': ['android', 'jni', 'androidx', 'java_', '__android__'],
      'ios': ['ios', 'objective-c', 'uikit', 'foundation', '__ios__', 'cocoa'],
      'windows': ['windows', 'win32', 'directx', 'msvc', '__windows__'],
      'mac': ['mac', 'macos', 'cocoa', 'appkit', '__apple__'],
      'linux': ['linux', 'x11', 'gtk', '__linux__'],
      'web': ['web', 'emscripten', 'webgl', '__emscripten__']
    };
    
    for (const [platform, indicators] of Object.entries(platformIndicators)) {
      if (indicators.some(indicator => contentLower.includes(indicator))) {
        return platform;
      }
    }
    
    return 'cross-platform';
  }
  
  /**
   * 提取版本信息
   */
  private extractVersionInfo(content: string): string | undefined {
    const versionPatterns = [
      /version["']?\s*[:=]\s*["']?([\d\.]+)/i,
      /@version\s+([\d\.]+)/i,
      /axmol[\s_-]?(?:version)?[\s_-]?([\d\.]+)/i,
      /v([\d]+\.[\d]+(?:\.[\d]+)?)/i
    ];
    
    for (const pattern of versionPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    
    return undefined;
  }

  /**
   * 提取代码示例 - 原方法保留兼容
   */
  private async extractCodeExample(file: any, feature: string, platform: string): Promise<CodeExample | null> {
    try {
      const response = await networkUtils.get(file.download_url, { timeout: 8000 });
      const content = response.data;

      // 检查内容是否包含相关特征
      if (!this.isContentRelevant(content, feature)) {
        return null;
      }

      // 提取相关代码段
      const codeSegments = this.extractRelevantCodeSegments(content, feature);
      if (codeSegments.length === 0) {
        return null;
      }

      const language = file.name.endsWith('.lua') ? 'lua' : 'cpp';
      
      return {
        title: this.generateExampleTitle(file.name, feature),
        language,
        code: codeSegments.join('\n\n// ...\n\n'),
        description: this.generateExampleDescription(file.name, feature, codeSegments),
        platform: this.detectPlatform(content, platform),
        version: 'latest',
        sourceUrl: file.html_url
      };

    } catch (error) {
      console.log(`⚠️ 提取代码示例失败: ${file.name}`);
      return null;
    }
  }

  /**
   * 检查内容是否相关
   */
  private isContentRelevant(content: string, feature: string): boolean {
    const contentLower = content.toLowerCase();
    const featureKeywords = this.extractFeatureKeywords(feature);
    
    return featureKeywords.some(keyword => contentLower.includes(keyword.toLowerCase()));
  }

  /**
   * 提取相关代码段
   */
  private extractRelevantCodeSegments(content: string, feature: string): string[] {
    const lines = content.split('\n');
    const segments: string[] = [];
    const featureKeywords = this.extractFeatureKeywords(feature);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineLower = line.toLowerCase();

      // 检查是否包含特征关键词
      if (featureKeywords.some(keyword => lineLower.includes(keyword.toLowerCase()))) {
        // 提取上下文（前后10行）
        const start = Math.max(0, i - 10);
        const end = Math.min(lines.length, i + 11);
        const segment = lines.slice(start, end).join('\n');
        
        segments.push(segment);
        i = end; // 跳过已处理的行
      }
    }

    return segments.slice(0, 3); // 限制段数
  }

  /**
   * 生成示例标题
   */
  private generateExampleTitle(filename: string, feature: string): string {
    const baseName = filename.replace(/\.(cpp|h|lua)$/, '');
    return `${feature} 示例 - ${baseName}`;
  }

  /**
   * 生成示例描述
   */
  private generateExampleDescription(filename: string, feature: string, segments: string[]): string {
    return `从 ${filename} 中提取的 ${feature} 相关代码示例，包含 ${segments.length} 个代码段。`;
  }

  /**
   * 检测平台
   */
  private detectPlatform(content: string, requestedPlatform: string): string {
    if (requestedPlatform !== 'all') return requestedPlatform;

    const contentLower = content.toLowerCase();
    
    if (contentLower.includes('android') || contentLower.includes('jni')) return 'android';
    if (contentLower.includes('ios') || contentLower.includes('objective-c')) return 'ios';
    if (contentLower.includes('windows') || contentLower.includes('win32')) return 'windows';
    if (contentLower.includes('mac') || contentLower.includes('cocoa')) return 'mac';
    if (contentLower.includes('linux')) return 'linux';
    if (contentLower.includes('web') || contentLower.includes('emscripten')) return 'web';
    
    return 'cross-platform';
  }

  /**
   * 高级测试代码搜索
   */
  private async searchAdvancedTestCode(feature: string, platform: string, options: SearchOptions): Promise<CodeExample[]> {
    const results: CodeExample[] = [];
    const testPaths = ['tests/cpp-tests', 'tests/lua-tests', 'tests/unit-tests', 'tests/performance-tests'];
    
    try {
      // 选择最相关的测试路径
      const relevantTestPaths = this.selectRelevantTestPaths(feature, testPaths);
      
      for (const path of relevantTestPaths.slice(0, 2)) {
        const pathResults = await this.searchEnhancedExamplesInPath(
          this.enhancedExampleSources.official.repo,
          path,
          feature,
          platform,
          options
        );
        results.push(...pathResults);
      }
      
      console.log(`🧪 高级测试代码搜索完成: ${results.length} 个示例`);
      
    } catch (error) {
      console.log('⚠️ 高级测试代码搜索失败:', error instanceof Error ? error.message : String(error));
    }
    
    return results;
  }
  
  /**
   * 选择相关测试路径
   */
  private selectRelevantTestPaths(feature: string, testPaths: string[]): string[] {
    const featureLower = feature.toLowerCase();
    const pathScores: { path: string; score: number }[] = [];
    
    for (const path of testPaths) {
      let score = 1;
      
      if (path.includes('cpp') && (featureLower.includes('cpp') || featureLower.includes('c++'))) score += 3;
      if (path.includes('lua') && featureLower.includes('lua')) score += 3;
      if (path.includes('performance') && featureLower.includes('performance')) score += 2;
      if (path.includes('unit') && featureLower.includes('unit')) score += 2;
      
      pathScores.push({ path, score });
    }
    
    return pathScores
      .sort((a, b) => b.score - a.score)
      .map(item => item.path);
  }
  
  /**
   * 搜索社区示例
   */
  private async searchCommunityExamples(feature: string, platform: string, options: SearchOptions): Promise<CodeExample[]> {
    const results: CodeExample[] = [];
    const communitySource = this.enhancedExampleSources.community;
    
    try {
      // 搜索社区示例项目
      const communityResults = await this.searchEnhancedExamplesInPath(
        communitySource.repo,
        '',
        feature,
        platform,
        options
      );
      results.push(...communityResults);
      
      console.log(`🌍 社区示例搜索完成: ${results.length} 个示例`);
      
    } catch (error) {
      console.log('⚠️ 社区示例搜索失败:', error instanceof Error ? error.message : String(error));
    }
    
    return results;
  }
  
  /**
   * 搜索文档示例
   */
  private async searchDocumentationExamples(feature: string, platform: string, options: SearchOptions): Promise<CodeExample[]> {
    const results: CodeExample[] = [];
    const docSource = this.enhancedExampleSources.documentation;
    
    try {
      for (const path of docSource.paths) {
        const pathResults = await this.searchEnhancedExamplesInPath(
          docSource.repo,
          path,
          feature,
          platform,
          options
        );
        results.push(...pathResults.slice(0, 2)); // 限制文档示例数量
      }
      
      console.log(`📄 文档示例搜索完成: ${results.length} 个示例`);
      
    } catch (error) {
      console.log('⚠️ 文档示例搜索失败:', error instanceof Error ? error.message : String(error));
    }
    
    return results;
  }

  /**
   * 搜索测试代码 - 原方法保留兼容
   */
  private async searchTestCode(feature: string, platform: string, options: SearchOptions): Promise<CodeExample[]> {
    return this.searchAdvancedTestCode(feature, platform, options);
  }

  /**
   * 增强的源码示例搜索
   */
  private async searchEnhancedSourceExamples(feature: string, platform: string, options: SearchOptions): Promise<CodeExample[]> {
    const results: CodeExample[] = [];
    
    // 智能源码路径选择
    const intelligentSourcePaths = this.selectIntelligentSourcePaths(feature);
    
    try {
      for (const pathInfo of intelligentSourcePaths.slice(0, 3)) {
        const pathResults = await this.searchEnhancedExamplesInPath(
          this.enhancedExampleSources.official.repo,
          pathInfo.path,
          feature,
          platform,
          { ...options, maxResults: 2 } // 限制每个路径的结果
        );
        
        // 根据路径相关性调整结果质量
        pathResults.forEach(result => {
          result.description = `[源码-${pathInfo.category}] ${result.description}`;
        });
        
        results.push(...pathResults);
      }
      
      console.log(`🔧 增强源码示例搜索完成: ${results.length} 个示例`);
      
    } catch (error) {
      console.log('⚠️ 增强源码示例搜索失败:', error instanceof Error ? error.message : String(error));
    }
    
    return results;
  }
  
  /**
   * 智能源码路径选择
   */
  private selectIntelligentSourcePaths(feature: string): Array<{ path: string; category: string; relevance: number }> {
    const allSourcePaths = [
      { path: 'core/2d', category: '2D渲染', keywords: ['sprite', 'texture', 'animation', 'draw', 'render'] },
      { path: 'core/3d', category: '3D渲染', keywords: ['3d', 'mesh', 'camera', 'light', 'shader'] },
      { path: 'core/ui', category: '用户界面', keywords: ['ui', 'widget', 'button', 'label', 'layout'] },
      { path: 'core/audio', category: '音频系统', keywords: ['audio', 'sound', 'music', 'effect'] },
      { path: 'core/physics', category: '物理引擎', keywords: ['physics', 'body', 'collision', 'joint'] },
      { path: 'core/base', category: '基础系统', keywords: ['director', 'scene', 'node', 'scheduler'] },
      { path: 'core/platform', category: '平台层', keywords: ['platform', 'file', 'thread', 'device'] },
      { path: 'core/network', category: '网络通信', keywords: ['http', 'websocket', 'download'] },
      { path: 'extensions', category: '扩展组件', keywords: ['extension', 'plugin', 'extra'] }
    ];
    
    const featureLower = feature.toLowerCase();
    const pathRelevance: Array<{ path: string; category: string; relevance: number }> = [];
    
    for (const pathInfo of allSourcePaths) {
      let relevance = 0;
      
      // 计算关键词匹配度
      pathInfo.keywords.forEach(keyword => {
        if (featureLower.includes(keyword)) {
          relevance += keyword.length; // 更长的关键词得分更高
        }
      });
      
      // 特殊加分逻辑
      if (pathInfo.path.includes('2d') && (featureLower.includes('sprite') || featureLower.includes('animation'))) {
        relevance += 10;
      }
      if (pathInfo.path.includes('ui') && featureLower.includes('界面')) {
        relevance += 8;
      }
      
      if (relevance > 0) {
        pathRelevance.push({ path: pathInfo.path, category: pathInfo.category, relevance });
      }
    }
    
    // 如果没有匹配的路径，返回默认的高优先级路径
    if (pathRelevance.length === 0) {
      return [
        { path: 'core/2d', category: '2D渲染', relevance: 1 },
        { path: 'core/base', category: '基础系统', relevance: 1 }
      ];
    }
    
    return pathRelevance.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * 搜索源码示例 - 原方法保留兼容
   */
  private async searchSourceCodeExamples(feature: string, platform: string, options: SearchOptions): Promise<CodeExample[]> {
    return this.searchEnhancedSourceExamples(feature, platform, options);
  }

  /**
   * 高级质量评估和排序
   */
  private advancedQualitySort(examples: CodeExample[], feature: string, searchStrategy: any): CodeExample[] {
    return examples.sort((a, b) => {
      const scoreA = this.calculateAdvancedQualityScore(a, feature, searchStrategy);
      const scoreB = this.calculateAdvancedQualityScore(b, feature, searchStrategy);
      
      return scoreB - scoreA;
    });
  }
  
  /**
   * 计算高级质量分数
   */
  private calculateAdvancedQualityScore(example: CodeExample, feature: string, searchStrategy: any): number {
    let score = 0;
    
    // 1. 内容相关性分数 (40%)
    const contentRelevance = this.calculateContentRelevanceScore(example, feature);
    score += contentRelevance * 0.4;
    
    // 2. 代码质量分数 (30%)
    const codeQuality = this.calculateCodeQualityScore(example);
    score += codeQuality * 0.3;
    
    // 3. 源信赖度分数 (20%)
    const sourceCredibility = this.calculateSourceCredibilityScore(example);
    score += sourceCredibility * 0.2;
    
    // 4. 复杂度匹配分数 (10%)
    const complexityMatch = this.calculateComplexityMatchScore(example, searchStrategy);
    score += complexityMatch * 0.1;
    
    return score;
  }
  
  /**
   * 计算内容相关性分数
   */
  private calculateContentRelevanceScore(example: CodeExample, feature: string): number {
    let score = 0;
    const enhancedKeywords = this.getEnhancedFeatureKeywords(feature);
    const titleLower = example.title.toLowerCase();
    const codeLower = example.code.toLowerCase();
    const descLower = example.description.toLowerCase();
    
    // 标题匹配（最高权重）
    enhancedKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      if (titleLower.includes(keywordLower)) {
        score += keyword === feature.toLowerCase() ? 50 : 25;
      }
    });
    
    // 代码内容匹配
    enhancedKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      const codeMatches = (codeLower.match(new RegExp(keywordLower, 'g')) || []).length;
      score += codeMatches * (keyword === feature.toLowerCase() ? 5 : 2);
    });
    
    // 描述匹配
    enhancedKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      if (descLower.includes(keywordLower)) {
        score += keyword === feature.toLowerCase() ? 10 : 5;
      }
    });
    
    return Math.min(score, 100); // 上限100分
  }
  
  /**
   * 计算代码质量分数
   */
  private calculateCodeQualityScore(example: CodeExample): number {
    let score = 0;
    const code = example.code;
    const lines = code.split('\n');
    
    // 代码长度分数（适中的长度最佳）
    const lineCount = lines.length;
    if (lineCount >= 10 && lineCount <= 100) {
      score += 30;
    } else if (lineCount > 100 && lineCount <= 200) {
      score += 20;
    } else if (lineCount > 5) {
      score += 10;
    }
    
    // 注释质量
    const commentLines = lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*'));
    const commentRatio = commentLines.length / lineCount;
    if (commentRatio > 0.1 && commentRatio < 0.5) {
      score += 20;
    }
    
    // 代码结构质量
    if (code.includes('class ') || code.includes('function ') || code.includes('def ')) {
      score += 15;
    }
    
    // 错误处理
    if (code.includes('try') || code.includes('catch') || code.includes('error')) {
      score += 10;
    }
    
    // 示例特征
    if (code.includes('example') || code.includes('demo') || code.includes('sample')) {
      score += 15;
    }
    
    return Math.min(score, 100);
  }
  
  /**
   * 计算源信赖度分数
   */
  private calculateSourceCredibilityScore(example: CodeExample): number {
    let score = 0;
    const sourceUrl = example.sourceUrl || '';
    
    // 官方源
    if (sourceUrl.includes('axmolengine/axmol')) {
      score += 50;
    }
    
    // 测试文件
    if (sourceUrl.includes('/tests/') || sourceUrl.includes('/test/')) {
      score += 30;
    }
    
    // 示例文件
    if (sourceUrl.includes('/examples/') || sourceUrl.includes('/samples/')) {
      score += 25;
    }
    
    // 文档文件
    if (sourceUrl.includes('/docs/') || sourceUrl.includes('/documentation/')) {
      score += 20;
    }
    
    // 核心源码
    if (sourceUrl.includes('/core/')) {
      score += 15;
    }
    
    return Math.min(score, 100);
  }
  
  /**
   * 计算复杂度匹配分数
   */
  private calculateComplexityMatchScore(example: CodeExample, searchStrategy: any): number {
    let score = 50; // 基础分数
    
    const codeComplexity = this.estimateCodeComplexity(example.code);
    const strategyComplexity = searchStrategy.complexity;
    
    // 复杂度匹配加分
    if (codeComplexity === strategyComplexity) {
      score += 30;
    } else if (
      (codeComplexity === 'intermediate' && strategyComplexity !== 'basic') ||
      (codeComplexity === 'basic' && strategyComplexity !== 'advanced')
    ) {
      score += 15;
    }
    
    return score;
  }
  
  /**
   * 估算代码复杂度
   */
  private estimateCodeComplexity(code: string): 'basic' | 'intermediate' | 'advanced' {
    const lines = code.split('\n').length;
    const functions = (code.match(/function|def|\w+\s*\(/g) || []).length;
    const classes = (code.match(/class\s+\w+/g) || []).length;
    const complexPatterns = (code.match(/if|for|while|switch|try|catch/g) || []).length;
    
    const complexityScore = lines * 0.5 + functions * 3 + classes * 5 + complexPatterns * 2;
    
    if (complexityScore > 100) return 'advanced';
    if (complexityScore > 30) return 'intermediate';
    return 'basic';
  }

  /**
   * 按质量排序示例 - 原方法保留兼容
   */
  private sortExamplesByQuality(examples: CodeExample[], feature: string): CodeExample[] {
    const searchStrategy = { complexity: 'moderate' }; // 默认策略
    return this.advancedQualitySort(examples, feature, searchStrategy);
  }
}

// 导出默认代码示例服务实例
export const codeExampleService = new CodeExampleService();
