/**
 * 社区解决方案服务
 * 负责搜索和提供来自社区的解决方案和讨论
 */

import { CommunitySolution, Solution, DiscussionThread, GitHubIssue, SearchOptions, ToolResponse } from '../types/index.js';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';

export class CommunityService {
  private readonly GITHUB_API_BASE = 'https://api.github.com';
  private readonly AXMOL_REPO = 'axmolengine/axmol';
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1小时缓存

  constructor() {
    // 初始化服务
  }

  /**
   * 搜索社区解决方案
   */
  async searchCommunitySolutions(
    problemDescription: string,
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🌐 搜索社区解决方案: "${problemDescription.substring(0, 50)}..."`);

      // 生成缓存键
      const cacheKey = `community_solutions_${this.hashString(problemDescription)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as CommunitySolution | null;
        if (cached) {
          console.log('✅ 从缓存获取社区解决方案');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.solutions.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      const sources: string[] = [];
      const solutions: Solution[] = [];
      const discussions: DiscussionThread[] = [];
      const relatedIssues: GitHubIssue[] = [];

      // 1. 搜索 GitHub Issues
      const githubSolutions = await this.searchGitHubIssues(problemDescription);
      if (githubSolutions.length > 0) {
        solutions.push(...githubSolutions);
        sources.push('github_issues');
      }

      // 2. 搜索 GitHub Discussions
      const githubDiscussions = await this.searchGitHubDiscussions(problemDescription);
      if (githubDiscussions.length > 0) {
        discussions.push(...githubDiscussions);
        sources.push('github_discussions');
      }

      // 3. 查找相关 Issues
      const issues = await this.findRelatedIssues(problemDescription);
      if (issues.length > 0) {
        relatedIssues.push(...issues);
        sources.push('related_issues');
      }

      // 4. 搜索其他社区平台（如果需要）
      const otherSolutions = await this.searchOtherCommunityPlatforms(problemDescription);
      if (otherSolutions.length > 0) {
        solutions.push(...otherSolutions);
        sources.push('community_platforms');
      }

      const communitySolution: CommunitySolution = {
        problem: problemDescription,
        solutions: this.sortSolutionsByRelevance(solutions, problemDescription),
        discussions: this.sortDiscussionsByRelevance(discussions, problemDescription),
        relatedIssues: this.sortIssuesByRelevance(relatedIssues, problemDescription)
      };

      // 缓存结果
      if (options.useCache !== false && (solutions.length > 0 || discussions.length > 0)) {
        await defaultCache.set(cacheKey, communitySolution, this.CACHE_TTL);
      }

      const totalResults = solutions.length + discussions.length + relatedIssues.length;
      console.log(`✅ 社区解决方案搜索完成: 找到 ${totalResults} 个结果`);

      return {
        success: true,
        data: communitySolution,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: totalResults,
          sources,
          cacheHit: false
        }
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'searchCommunitySolutions', { problemDescription, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 搜索 GitHub Issues
   */
  private async searchGitHubIssues(problemDescription: string): Promise<Solution[]> {
    const solutions: Solution[] = [];

    try {
      const searchQuery = this.buildGitHubSearchQuery(problemDescription, 'issue');
      const searchUrl = `${this.GITHUB_API_BASE}/search/issues?q=${encodeURIComponent(searchQuery)}&sort=updated&per_page=8`; // 减少结果数量

      const response = await networkUtils.get(searchUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 8000 // 减少超时时间
      });

      const issues = response.data.items || [];

      for (const issue of issues.slice(0, 5)) {
        // 只处理已关闭的 issues（通常意味着已解决）
        if (issue.state === 'closed') {
          const solution = await this.extractSolutionFromIssue(issue);
          if (solution) {
            solutions.push(solution);
          }
        }
      }

      console.log(`🔍 从 GitHub Issues 找到 ${solutions.length} 个解决方案`);

    } catch (error) {
      console.log('⚠️ GitHub Issues 搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return solutions;
  }

  /**
   * 搜索 GitHub Discussions
   */
  private async searchGitHubDiscussions(problemDescription: string): Promise<DiscussionThread[]> {
    const discussions: DiscussionThread[] = [];

    try {
      // GitHub Discussions API 需要 GraphQL，这里简化处理
      // 实际实现中可以使用 GraphQL API 搜索 discussions
      
      // 作为替代，搜索 discussions 页面
      const discussionsUrl = `https://github.com/${this.AXMOL_REPO}/discussions`;
      
      // 这里可以实现网页抓取或使用 GraphQL API
      // 目前返回模拟数据
      discussions.push({
        title: '社区讨论：相关问题解决方案',
        url: discussionsUrl,
        platform: 'github',
        replies: 0,
        lastActivity: new Date().toISOString(),
        tags: ['community', 'discussion']
      });

      console.log(`💬 从 GitHub Discussions 找到 ${discussions.length} 个讨论`);

    } catch (error) {
      console.log('⚠️ GitHub Discussions 搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return discussions;
  }

  /**
   * 查找相关 Issues
   */
  private async findRelatedIssues(problemDescription: string): Promise<GitHubIssue[]> {
    const issues: GitHubIssue[] = [];

    try {
      const searchQuery = this.buildGitHubSearchQuery(problemDescription, 'issue');
      const searchUrl = `${this.GITHUB_API_BASE}/search/issues?q=${encodeURIComponent(searchQuery)}&sort=updated&per_page=15`;

      const response = await networkUtils.get(searchUrl, {
        headers: networkUtils.getGitHubHeaders(),
        timeout: 10000
      });

      const searchResults = response.data.items || [];

      for (const issue of searchResults) {
        issues.push({
          number: issue.number,
          title: issue.title,
          state: issue.state,
          labels: issue.labels?.map((label: any) => label.name) || [],
          url: issue.html_url,
          createdAt: issue.created_at,
          updatedAt: issue.updated_at
        });
      }

      console.log(`🔗 找到 ${issues.length} 个相关 Issues`);

    } catch (error) {
      console.log('⚠️ 相关 Issues 搜索失败:', error instanceof Error ? error.message : String(error));
    }

    return issues;
  }

  /**
   * 搜索其他社区平台
   */
  private async searchOtherCommunityPlatforms(problemDescription: string): Promise<Solution[]> {
    const solutions: Solution[] = [];

    // 这里可以实现搜索其他平台如 Discord、Reddit、Stack Overflow 等
    // 目前返回空数组，后续可以扩展

    return solutions;
  }

  /**
   * 构建 GitHub 搜索查询
   */
  private buildGitHubSearchQuery(problemDescription: string, type: 'issue' | 'discussion'): string {
    const keywords = this.extractKeywords(problemDescription);
    const query = [
      `repo:${this.AXMOL_REPO}`,
      `is:${type}`,
      ...keywords.slice(0, 5) // 限制关键词数量
    ].join(' ');

    return query;
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    const keywords: string[] = [];
    
    // 移除常见的停用词
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];
    
    // 提取单词
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.includes(word));

    // 添加技术相关的关键词
    const techKeywords = words.filter(word => 
      word.includes('error') || 
      word.includes('fail') || 
      word.includes('crash') || 
      word.includes('bug') ||
      word.includes('issue') ||
      word.includes('problem') ||
      word.includes('sprite') ||
      word.includes('node') ||
      word.includes('scene') ||
      word.includes('audio') ||
      word.includes('render')
    );

    keywords.push(...techKeywords);
    keywords.push(...words.slice(0, 3)); // 添加前3个一般关键词

    return [...new Set(keywords)]; // 去重
  }

  /**
   * 从 Issue 中提取解决方案
   */
  private async extractSolutionFromIssue(issue: any): Promise<Solution | null> {
    try {
      // 获取 Issue 的评论来寻找解决方案
      const commentsUrl = issue.comments_url;
      let solutionText = issue.body || '';
      let author = issue.user?.login || 'Unknown';

      if (issue.comments > 0) {
        try {
          const commentsResponse = await networkUtils.get(commentsUrl, {
            headers: networkUtils.getGitHubHeaders(),
            timeout: 8000
          });

          const comments = commentsResponse.data || [];
          
          // 查找包含解决方案的评论
          for (const comment of comments) {
            const commentBody = comment.body?.toLowerCase() || '';
            if (commentBody.includes('solution') || 
                commentBody.includes('solved') || 
                commentBody.includes('fix') ||
                commentBody.includes('resolve')) {
              solutionText = comment.body;
              author = comment.user?.login || author;
              break;
            }
          }
        } catch (error) {
          // 忽略评论获取失败
        }
      }

      return {
        title: `GitHub Issue 解决方案: ${issue.title}`,
        author,
        description: solutionText.substring(0, 500),
        code: [],
        votes: issue.reactions?.total_count || 0,
        verified: issue.state === 'closed',
        sourceUrl: issue.html_url,
        tags: issue.labels?.map((label: any) => label.name) || []
      };

    } catch (error) {
      return null;
    }
  }

  /**
   * 按相关性排序解决方案
   */
  private sortSolutionsByRelevance(solutions: Solution[], problemDescription: string): Solution[] {
    return solutions.sort((a, b) => {
      // 计算相关性分数
      const aScore = this.calculateSolutionRelevance(a, problemDescription);
      const bScore = this.calculateSolutionRelevance(b, problemDescription);
      
      return bScore - aScore;
    });
  }

  /**
   * 计算解决方案相关性
   */
  private calculateSolutionRelevance(solution: Solution, problemDescription: string): number {
    let score = 0;
    const problemLower = problemDescription.toLowerCase();
    const titleLower = solution.title.toLowerCase();
    const descLower = solution.description.toLowerCase();

    // 标题匹配
    if (titleLower.includes(problemLower.substring(0, 20))) {
      score += 10;
    }

    // 描述匹配
    const keywords = this.extractKeywords(problemDescription);
    keywords.forEach(keyword => {
      if (titleLower.includes(keyword)) score += 3;
      if (descLower.includes(keyword)) score += 2;
    });

    // 验证状态加分
    if (solution.verified) score += 5;

    // 投票数加分
    score += Math.min(solution.votes * 0.1, 5);

    return score;
  }

  /**
   * 按相关性排序讨论
   */
  private sortDiscussionsByRelevance(discussions: DiscussionThread[], problemDescription: string): DiscussionThread[] {
    return discussions.sort((a, b) => {
      const aScore = this.calculateDiscussionRelevance(a, problemDescription);
      const bScore = this.calculateDiscussionRelevance(b, problemDescription);
      
      return bScore - aScore;
    });
  }

  /**
   * 计算讨论相关性
   */
  private calculateDiscussionRelevance(discussion: DiscussionThread, problemDescription: string): number {
    let score = 0;
    const problemLower = problemDescription.toLowerCase();
    const titleLower = discussion.title.toLowerCase();

    // 标题匹配
    if (titleLower.includes(problemLower.substring(0, 20))) {
      score += 10;
    }

    // 关键词匹配
    const keywords = this.extractKeywords(problemDescription);
    keywords.forEach(keyword => {
      if (titleLower.includes(keyword)) score += 2;
    });

    // 回复数加分
    score += Math.min(discussion.replies * 0.1, 3);

    // 最近活动加分
    const daysSinceActivity = (Date.now() - new Date(discussion.lastActivity).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceActivity < 30) score += 2;

    return score;
  }

  /**
   * 按相关性排序 Issues
   */
  private sortIssuesByRelevance(issues: GitHubIssue[], problemDescription: string): GitHubIssue[] {
    return issues.sort((a, b) => {
      const aScore = this.calculateIssueRelevance(a, problemDescription);
      const bScore = this.calculateIssueRelevance(b, problemDescription);
      
      return bScore - aScore;
    });
  }

  /**
   * 计算 Issue 相关性
   */
  private calculateIssueRelevance(issue: GitHubIssue, problemDescription: string): number {
    let score = 0;
    const problemLower = problemDescription.toLowerCase();
    const titleLower = issue.title.toLowerCase();

    // 标题匹配
    if (titleLower.includes(problemLower.substring(0, 20))) {
      score += 10;
    }

    // 关键词匹配
    const keywords = this.extractKeywords(problemDescription);
    keywords.forEach(keyword => {
      if (titleLower.includes(keyword)) score += 2;
    });

    // 已关闭的 issue 加分（通常意味着已解决）
    if (issue.state === 'closed') score += 5;

    // 标签匹配
    const relevantLabels = ['bug', 'enhancement', 'question', 'help wanted'];
    issue.labels.forEach(label => {
      if (relevantLabels.includes(label.toLowerCase())) score += 1;
    });

    return score;
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
}

// 导出默认社区服务实例
export const communityService = new CommunityService();
