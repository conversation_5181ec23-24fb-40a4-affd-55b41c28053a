/**
 * 语义搜索和知识库管理服务
 * 提供高级语义搜索、知识图谱构建和智能推荐功能
 */

import { AxmolResource, SearchOptions, ToolResponse } from '../types/index.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
import { dataSourceManager } from '../utils/dataSourceManager.js';

// 语义搜索接口
interface SemanticSearchQuery {
  query: string;
  intent?: 'search' | 'learn' | 'implement' | 'debug' | 'optimize';
  context?: string[];
  filters?: {
    type?: string[];
    platform?: string[];
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
  };
}

// 知识节点
interface KnowledgeNode {
  id: string;
  title: string;
  content: string;
  type: 'concept' | 'api' | 'example' | 'tutorial' | 'best_practice';
  category: string;
  tags: string[];
  relationships: KnowledgeRelationship[];
  importance: number;
  lastUpdated: Date;
}

// 知识关系
interface KnowledgeRelationship {
  targetId: string;
  type: 'related' | 'prerequisite' | 'builds_on' | 'example_of' | 'opposite_of';
  strength: number; // 0-1 关系强度
}

// 语义搜索结果
interface SemanticSearchResult {
  resources: AxmolResource[];
  suggestions: string[];
  relatedConcepts: string[];
  learningPath?: string[];
  confidence: number;
}

export class SemanticSearchService {
  private knowledgeGraph: Map<string, KnowledgeNode> = new Map();
  private conceptEmbeddings: Map<string, number[]> = new Map();
  private intentClassifier: Map<string, RegExp[]> = new Map();
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1小时缓存

  constructor() {
    this.initializeKnowledgeGraph();
    this.initializeIntentClassifier();
    this.initializeConceptMapping();
  }

  /**
   * 执行语义搜索
   */
  async performSemanticSearch(
    searchQuery: SemanticSearchQuery,
    options: SearchOptions = {}
  ): Promise<ToolResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🧠 执行语义搜索: ${searchQuery.query}`);

      // 生成缓存键
      const cacheKey = `semantic_search_${this.hashQuery(searchQuery)}`;
      
      // 尝试从缓存获取
      if (options.useCache !== false) {
        const cached = await defaultCache.get(cacheKey) as SemanticSearchResult | null;
        if (cached) {
          console.log('✅ 从缓存获取语义搜索结果');
          return {
            success: true,
            data: cached,
            metadata: {
              searchTime: Date.now() - startTime,
              resultsCount: cached.resources.length,
              sources: ['cache'],
              cacheHit: true
            }
          };
        }
      }

      // 1. 意图识别
      const detectedIntent = this.detectIntent(searchQuery.query);
      const finalIntent = searchQuery.intent || detectedIntent;
      
      // 2. 查询扩展
      const expandedQuery = await this.expandQuery(searchQuery.query, finalIntent);
      
      // 3. 语义相似度搜索
      const semanticResults = await this.findSemanticMatches(expandedQuery, searchQuery.filters);
      
      // 4. 生成学习路径
      const learningPath = this.generateLearningPath(expandedQuery.concepts, finalIntent);
      
      // 5. 智能建议
      const suggestions = this.generateSuggestions(searchQuery.query, semanticResults);
      
      // 6. 相关概念发现
      const relatedConcepts = this.findRelatedConcepts(expandedQuery.concepts);

      const result: SemanticSearchResult = {
        resources: semanticResults,
        suggestions,
        relatedConcepts,
        learningPath,
        confidence: this.calculateConfidence(semanticResults, expandedQuery)
      };

      // 缓存结果
      if (options.useCache !== false && semanticResults.length > 0) {
        await defaultCache.set(cacheKey, result, this.CACHE_TTL);
      }

      console.log(`✅ 语义搜索完成: 找到 ${semanticResults.length} 个相关资源`);

      return {
        success: true,
        data: result,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: semanticResults.length,
          sources: ['semantic_search'],
          cacheHit: false
        } as any
      };

    } catch (error) {
      const axmolError = errorHandler.handleApiError(error, 'performSemanticSearch', { searchQuery, options });
      
      return {
        success: false,
        error: axmolError,
        metadata: {
          searchTime: Date.now() - startTime,
          resultsCount: 0,
          sources: [],
          cacheHit: false
        }
      };
    }
  }

  /**
   * 构建知识图谱
   */
  async buildKnowledgeGraph(resources: AxmolResource[]): Promise<void> {
    console.log(`🔨 构建知识图谱: ${resources.length} 个资源`);
    
    for (const resource of resources) {
      const node = this.createKnowledgeNode(resource);
      this.knowledgeGraph.set(node.id, node);
      
      // 建立关系
      await this.establishRelationships(node);
    }
    
    console.log(`✅ 知识图谱构建完成: ${this.knowledgeGraph.size} 个节点`);
  }

  /**
   * 获取推荐内容
   */
  getRecommendations(currentResource: string, intent: string = 'learn'): string[] {
    const currentNode = this.knowledgeGraph.get(currentResource);
    if (!currentNode) return [];

    const recommendations: Array<{ id: string; score: number }> = [];

    // 基于关系的推荐
    for (const relationship of currentNode.relationships) {
      const targetNode = this.knowledgeGraph.get(relationship.targetId);
      if (!targetNode) continue;

      let score = relationship.strength * targetNode.importance;
      
      // 根据意图调整评分
      if (intent === 'learn' && relationship.type === 'builds_on') {
        score *= 1.5;
      } else if (intent === 'implement' && relationship.type === 'example_of') {
        score *= 1.3;
      }
      
      recommendations.push({ id: relationship.targetId, score });
    }

    // 排序并返回前10个
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 10)
      .map(rec => rec.id);
  }

  /**
   * 初始化知识图谱
   */
  private initializeKnowledgeGraph(): void {
    // 核心概念节点
    const coreNodes: Partial<KnowledgeNode>[] = [
      {
        id: 'sprite',
        title: 'Sprite精灵',
        content: 'Axmol中的基础显示对象，用于显示2D图像',
        type: 'concept',
        category: 'core',
        tags: ['2d', 'graphics', 'basic'],
        importance: 0.9
      },
      {
        id: 'scene',
        title: 'Scene场景',
        content: 'Axmol中的场景管理系统，包含所有游戏对象',
        type: 'concept',
        category: 'core',
        tags: ['scene', 'management', 'basic'],
        importance: 0.95
      },
      {
        id: 'node',
        title: 'Node节点',
        content: 'Axmol中所有显示对象的基类',
        type: 'concept',
        category: 'core',
        tags: ['base', 'hierarchy', 'basic'],
        importance: 1.0
      },
      {
        id: 'animation',
        title: 'Animation动画',
        content: 'Axmol中的动画系统，包括帧动画和补间动画',
        type: 'concept',
        category: 'animation',
        tags: ['animation', 'visual', 'intermediate'],
        importance: 0.8
      },
      {
        id: 'action',
        title: 'Action动作',
        content: 'Axmol中的动作系统，用于对象的移动、旋转等',
        type: 'concept',
        category: 'animation',
        tags: ['action', 'movement', 'intermediate'],
        importance: 0.85
      }
    ];

    for (const nodeData of coreNodes) {
      const node: KnowledgeNode = {
        id: nodeData.id!,
        title: nodeData.title!,
        content: nodeData.content!,
        type: nodeData.type!,
        category: nodeData.category!,
        tags: nodeData.tags!,
        relationships: [],
        importance: nodeData.importance!,
        lastUpdated: new Date()
      };
      
      this.knowledgeGraph.set(node.id, node);
    }

    // 建立基础关系
    this.establishBasicRelationships();
    
    console.log(`📚 初始化知识图谱: ${this.knowledgeGraph.size} 个核心概念`);
  }

  /**
   * 建立基础关系
   */
  private establishBasicRelationships(): void {
    const relationships = [
      { from: 'sprite', to: 'node', type: 'builds_on', strength: 0.9 },
      { from: 'scene', to: 'node', type: 'builds_on', strength: 0.8 },
      { from: 'animation', to: 'sprite', type: 'related', strength: 0.7 },
      { from: 'action', to: 'node', type: 'related', strength: 0.8 },
      { from: 'action', to: 'animation', type: 'related', strength: 0.6 }
    ];

    for (const rel of relationships) {
      const fromNode = this.knowledgeGraph.get(rel.from);
      const toNode = this.knowledgeGraph.get(rel.to);
      
      if (fromNode && toNode) {
        fromNode.relationships.push({
          targetId: rel.to,
          type: rel.type as any,
          strength: rel.strength
        });
        
        // 建立反向关系
        toNode.relationships.push({
          targetId: rel.from,
          type: 'related',
          strength: rel.strength * 0.8
        });
      }
    }
  }

  /**
   * 初始化意图分类器
   */
  private initializeIntentClassifier(): void {
    this.intentClassifier.set('search', [
      /如何|怎么|怎样|查找|搜索/,
      /what|how|where|find/i
    ]);
    
    this.intentClassifier.set('learn', [
      /学习|教程|入门|基础|了解/,
      /learn|tutorial|guide|basic|understand/i
    ]);
    
    this.intentClassifier.set('implement', [
      /实现|编写|创建|开发|代码/,
      /implement|create|develop|code|write/i
    ]);
    
    this.intentClassifier.set('debug', [
      /调试|错误|问题|bug|修复/,
      /debug|error|problem|bug|fix/i
    ]);
    
    this.intentClassifier.set('optimize', [
      /优化|性能|提升|改进/,
      /optimize|performance|improve|enhance/i
    ]);
  }

  /**
   * 初始化概念映射
   */
  private initializeConceptMapping(): void {
    // 简单的概念向量映射（实际应用中会使用更复杂的嵌入）
    const concepts = [
      'sprite', 'node', 'scene', 'animation', 'action', 'layer', 'director',
      'texture', 'render', 'event', 'touch', 'physics', 'sound', 'network'
    ];

    concepts.forEach((concept, index) => {
      // 生成简单的向量表示
      const vector = new Array(50).fill(0).map((_, i) => 
        Math.sin((index + 1) * (i + 1) * 0.1) * Math.cos(i * 0.05)
      );
      this.conceptEmbeddings.set(concept, vector);
    });
  }

  /**
   * 意图检测
   */
  private detectIntent(query: string): string {
    for (const [intent, patterns] of this.intentClassifier.entries()) {
      for (const pattern of patterns) {
        if (pattern.test(query)) {
          return intent;
        }
      }
    }
    return 'search'; // 默认意图
  }

  /**
   * 查询扩展
   */
  private async expandQuery(query: string, intent: string): Promise<{
    original: string;
    expanded: string[];
    concepts: string[];
  }> {
    const concepts = this.extractConcepts(query);
    const expanded: string[] = [query];

    // 基于概念的扩展
    for (const concept of concepts) {
      const node = this.knowledgeGraph.get(concept);
      if (node) {
        // 添加同义词和相关术语
        expanded.push(...node.tags);
        
        // 根据意图添加相关概念
        for (const rel of node.relationships) {
          if (intent === 'learn' && rel.type === 'prerequisite') {
            const prereqNode = this.knowledgeGraph.get(rel.targetId);
            if (prereqNode) expanded.push(prereqNode.title);
          }
        }
      }
    }

    return {
      original: query,
      expanded: [...new Set(expanded)], // 去重
      concepts
    };
  }

  /**
   * 提取概念
   */
  private extractConcepts(query: string): string[] {
    const queryLower = query.toLowerCase();
    const concepts: string[] = [];

    for (const [conceptId, node] of this.knowledgeGraph.entries()) {
      // 检查标题匹配
      if (queryLower.includes(node.title.toLowerCase())) {
        concepts.push(conceptId);
        continue;
      }
      
      // 检查标签匹配
      for (const tag of node.tags) {
        if (queryLower.includes(tag.toLowerCase())) {
          concepts.push(conceptId);
          break;
        }
      }
    }

    return concepts;
  }

  /**
   * 语义匹配搜索
   */
  private async findSemanticMatches(
    expandedQuery: any,
    filters?: any
  ): Promise<AxmolResource[]> {
    const results: AxmolResource[] = [];
    
    // 这里应该调用实际的搜索服务
    // 为演示目的，返回基于概念的模拟结果
    for (const concept of expandedQuery.concepts) {
      const node = this.knowledgeGraph.get(concept);
      if (node) {
        // 创建模拟资源
        const resource: AxmolResource = {
          title: `${node.title} - 使用指南`,
          url: `https://docs.axmol.dev/${concept}`,
          content: node.content,
          type: 'official_docs',
          source: 'semantic_search',
          relevanceScore: node.importance * 8 + Math.random() * 2,
          matchedTerms: [concept, ...node.tags.slice(0, 3)]
        };
        results.push(resource);
      }
    }

    // 按相关性排序
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * 生成学习路径
   */
  private generateLearningPath(concepts: string[], intent: string): string[] {
    if (intent !== 'learn' || concepts.length === 0) return [];

    const path: string[] = [];
    const visited = new Set<string>();

    // 从基础概念开始
    const startConcept = this.findMostBasicConcept(concepts);
    if (startConcept) {
      this.buildLearningPath(startConcept, path, visited, 5);
    }

    return path;
  }

  /**
   * 查找最基础的概念
   */
  private findMostBasicConcept(concepts: string[]): string | null {
    let mostBasic: string | null = null;
    let maxImportance = 0;

    for (const concept of concepts) {
      const node = this.knowledgeGraph.get(concept);
      if (node && node.importance > maxImportance) {
        // 检查是否是基础概念（较少前置条件）
        const prerequisites = node.relationships.filter(r => r.type === 'prerequisite');
        if (prerequisites.length <= 2) {
          mostBasic = concept;
          maxImportance = node.importance;
        }
      }
    }

    return mostBasic;
  }

  /**
   * 构建学习路径
   */
  private buildLearningPath(
    currentConcept: string,
    path: string[],
    visited: Set<string>,
    maxDepth: number
  ): void {
    if (maxDepth <= 0 || visited.has(currentConcept)) return;

    path.push(currentConcept);
    visited.add(currentConcept);

    const node = this.knowledgeGraph.get(currentConcept);
    if (!node) return;

    // 查找下一个学习目标
    const nextConcepts = node.relationships
      .filter(r => r.type === 'builds_on' && !visited.has(r.targetId))
      .sort((a, b) => b.strength - a.strength);

    if (nextConcepts.length > 0) {
      this.buildLearningPath(nextConcepts[0].targetId, path, visited, maxDepth - 1);
    }
  }

  /**
   * 生成建议
   */
  private generateSuggestions(originalQuery: string, results: AxmolResource[]): string[] {
    const suggestions: string[] = [];

    // 基于结果生成相关查询建议
    if (results.length > 0) {
      const commonTerms = this.extractCommonTerms(results);
      
      suggestions.push(
        `${originalQuery} 示例`,
        `${originalQuery} 最佳实践`,
        `${originalQuery} 常见问题`,
        ...commonTerms.slice(0, 3).map(term => `${term} 教程`)
      );
    } else {
      // 无结果时的通用建议
      suggestions.push(
        '检查拼写是否正确',
        '尝试使用更通用的关键词',
        '查看相关API文档',
        '浏览示例代码'
      );
    }

    return suggestions.slice(0, 5);
  }

  /**
   * 提取常用术语
   */
  private extractCommonTerms(results: AxmolResource[]): string[] {
    const termCount = new Map<string, number>();

    for (const result of results) {
      for (const term of result.matchedTerms) {
        termCount.set(term, (termCount.get(term) || 0) + 1);
      }
    }

    return Array.from(termCount.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([term]) => term);
  }

  /**
   * 查找相关概念
   */
  private findRelatedConcepts(concepts: string[]): string[] {
    const related = new Set<string>();

    for (const concept of concepts) {
      const node = this.knowledgeGraph.get(concept);
      if (node) {
        for (const rel of node.relationships) {
          if (rel.type === 'related' && rel.strength > 0.5) {
            const relatedNode = this.knowledgeGraph.get(rel.targetId);
            if (relatedNode) {
              related.add(relatedNode.title);
            }
          }
        }
      }
    }

    return Array.from(related).slice(0, 8);
  }

  /**
   * 创建知识节点
   */
  private createKnowledgeNode(resource: AxmolResource): KnowledgeNode {
    return {
      id: this.generateNodeId(resource),
      title: resource.title,
      content: resource.content || '',
      type: this.inferNodeType(resource),
      category: this.inferCategory(resource),
      tags: resource.matchedTerms || [],
      relationships: [],
      importance: resource.relevanceScore / 10,
      lastUpdated: new Date()
    };
  }

  /**
   * 生成节点ID
   */
  private generateNodeId(resource: AxmolResource): string {
    return resource.url.replace(/[^a-zA-Z0-9]/g, '_');
  }

  /**
   * 推断节点类型
   */
  private inferNodeType(resource: AxmolResource): KnowledgeNode['type'] {
    if (resource.type === 'example') return 'example';
    if (resource.url.includes('tutorial')) return 'tutorial';
    if (resource.url.includes('api')) return 'api';
    if (resource.title.includes('最佳实践')) return 'best_practice';
    return 'concept';
  }

  /**
   * 推断类别
   */
  private inferCategory(resource: AxmolResource): string {
    const title = resource.title.toLowerCase();
    const content = (resource.content || '').toLowerCase();
    
    if (title.includes('sprite') || content.includes('sprite')) return 'graphics';
    if (title.includes('scene') || content.includes('scene')) return 'scene';
    if (title.includes('animation') || content.includes('animation')) return 'animation';
    if (title.includes('physics') || content.includes('physics')) return 'physics';
    if (title.includes('sound') || content.includes('audio')) return 'audio';
    
    return 'general';
  }

  /**
   * 建立关系
   */
  private async establishRelationships(node: KnowledgeNode): Promise<void> {
    // 基于内容相似度建立关系
    for (const [existingId, existingNode] of this.knowledgeGraph.entries()) {
      if (existingId === node.id) continue;

      const similarity = this.calculateContentSimilarity(node, existingNode);
      if (similarity > 0.6) {
        node.relationships.push({
          targetId: existingId,
          type: 'related',
          strength: similarity
        });
      }
    }
  }

  /**
   * 计算内容相似度
   */
  private calculateContentSimilarity(node1: KnowledgeNode, node2: KnowledgeNode): number {
    // 简单的基于标签重叠的相似度计算
    const tags1 = new Set(node1.tags);
    const tags2 = new Set(node2.tags);
    
    const intersection = new Set([...tags1].filter(tag => tags2.has(tag)));
    const union = new Set([...tags1, ...tags2]);
    
    return union.size > 0 ? intersection.size / union.size : 0;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(results: AxmolResource[], expandedQuery: any): number {
    if (results.length === 0) return 0;

    const avgRelevance = results.reduce((sum, r) => sum + r.relevanceScore, 0) / results.length;
    const conceptCoverage = expandedQuery.concepts.length > 0 ? 
      results.filter(r => r.matchedTerms.some(term => expandedQuery.concepts.includes(term))).length / results.length : 0;

    return Math.min((avgRelevance / 10 * 0.7) + (conceptCoverage * 0.3), 1.0);
  }

  /**
   * 查询哈希
   */
  private hashQuery(query: SemanticSearchQuery): string {
    const str = JSON.stringify(query);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }
}

// 导出语义搜索服务实例
export const semanticSearchService = new SemanticSearchService();

// 导出类型
export type {
  SemanticSearchQuery,
  SemanticSearchResult,
  KnowledgeNode,
  KnowledgeRelationship
};