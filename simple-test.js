/**
 * 简单测试相关性修复
 */

console.log("🧪 测试相关性计算修复效果\n");

// 模拟修复后的相关性计算
function calculateRelevance(content, query) {
    const contentLower = content.toLowerCase();
    const queryTerms = extractSearchTerms(query);
    let score = 0;

    queryTerms.forEach(term => {
        if (term.length < 2) return;

        let termScore = 0;

        // 精确匹配 - 提高权重
        const exactMatches = contentLower.match(new RegExp(term, 'gi'));
        if (exactMatches) {
            termScore += exactMatches.length * 5; // 提高精确匹配权重
        }

        // 部分匹配
        const partialTerm = term.substring(0, Math.max(3, term.length - 2));
        const partialMatches = contentLower.match(new RegExp(partialTerm, 'gi'));
        if (partialMatches && !exactMatches) {
            termScore += partialMatches.length * 2; // 提高部分匹配权重
        }

        // 游戏引擎相关术语的特殊处理
        const gameEngineScore = calculateGameEngineRelevance(contentLower, term);
        if (gameEngineScore > 0) {
            termScore += gameEngineScore * 2; // 提高相关术语权重
        }

        // 精灵动画相关的特殊处理
        const spriteAnimationScore = calculateSpriteAnimationRelevance(contentLower, term);
        if (spriteAnimationScore > 0) {
            termScore += spriteAnimationScore * 3; // 精灵动画相关术语高权重
        }

        score += termScore;
    });

    // 如果是游戏引擎相关的查询但没有直接匹配，给予基础分数
    if (score === 0 && isGameEngineRelated(query)) {
        score = 3; // 提高基础相关性分数
    }

    // 精灵动画相关查询的特殊处理
    if (isSpriteAnimationRelated(query)) {
        score += 5; // 为精灵动画相关查询增加基础分数
    }

    return score;
}

function extractSearchTerms(query) {
    const terms = [];
    const spaceSplit = query.toLowerCase().split(/\s+/);
    terms.push(...spaceSplit);
    const englishWords = query.match(/[a-zA-Z]+/g);
    if (englishWords) {
        terms.push(...englishWords.map(w => w.toLowerCase()));
    }
    return [...new Set(terms)].filter(term => term.length >= 2);
}

function calculateGameEngineRelevance(content, term) {
    const gameEngineTerms = {
        'sprite': ['node', 'texture', 'image', 'render', '2d', 'game', 'engine', 'spriteframe', 'spritebatchnode'],
        'animation': ['action', 'tween', 'frame', 'sequence', 'game', 'animate', 'animationframe'],
        'frame': ['sprite', 'animation', 'texture', 'sequence'],
        'sheet': ['sprite', 'texture', 'atlas', 'frame', 'plist'],
        '精灵': ['sprite', 'node', 'texture', 'image'],
        '动画': ['animation', 'animate', 'action', 'frame'],
        '图集': ['sheet', 'atlas', 'texture', 'sprite']
    };

    if (gameEngineTerms[term]) {
        let relatedScore = 0;
        gameEngineTerms[term].forEach(relatedTerm => {
            if (content.includes(relatedTerm)) {
                relatedScore += 1.5;
            }
        });
        return relatedScore;
    }
    return 0;
}

function calculateSpriteAnimationRelevance(content, term) {
    const spriteAnimationTerms = {
        'sprite': ['spriteframe', 'spritebatchnode', 'animation', 'animate', 'texture', 'atlas'],
        'animation': ['spriteframe', 'animate', 'action', 'sequence', 'frame', 'duration'],
        'frame': ['spriteframe', 'animation', 'sequence', 'texture', 'atlas'],
        'sheet': ['spriteframe', 'texture', 'atlas', 'plist', 'animation'],
        '精灵': ['spriteframe', 'animation', 'texture'],
        '动画': ['spriteframe', 'animate', 'action', 'frame']
    };

    if (spriteAnimationTerms[term]) {
        let relatedScore = 0;
        spriteAnimationTerms[term].forEach(relatedTerm => {
            if (content.includes(relatedTerm)) {
                relatedScore += 2.0;
            }
        });
        return relatedScore;
    }
    return 0;
}

function isGameEngineRelated(query) {
    const gameEngineKeywords = [
        'sprite', 'animation', 'scene', 'texture', 'sound', 'physics', 'ui',
        'camera', 'light', 'mesh', 'render', 'game', 'engine', '2d', '3d',
        'cocos', 'axmol', 'opengl', 'metal', 'shader', 'vertex', 'fragment',
        'frame', 'sheet', 'atlas', 'plist', '精灵', '动画', '图集', '帧'
    ];
    const queryLower = query.toLowerCase();
    return gameEngineKeywords.some(keyword => queryLower.includes(keyword));
}

function isSpriteAnimationRelated(query) {
    const spriteAnimationKeywords = [
        'sprite', 'animation', 'animate', 'frame', 'sheet', 'atlas', 'plist',
        'spriteframe', 'spritebatchnode', 'texture', 'sequence', 'duration',
        '精灵', '动画', '图集', '帧', '角色', '播放'
    ];
    const queryLower = query.toLowerCase();
    return spriteAnimationKeywords.some(keyword => queryLower.includes(keyword));
}

// 测试数据
const testContent = `
Axmol Engine API Documentation

Sprite Class
The Sprite class is used to display 2D images and textures in your game.
It supports sprite frames, animations, and batch rendering.

SpriteFrame Class
SpriteFrame represents a single frame in a sprite sheet or texture atlas.
It contains information about the texture region and can be used with Animation.

Animation Class
The Animation class is used to create frame-based animations for sprites.
It works with SpriteFrame objects to create smooth animated sequences.

Example Usage:
SpriteFrameCache::getInstance()->addSpriteFramesWithFile("character.plist");
auto sprite = Sprite::createWithSpriteFrameName("character_01.png");
auto animation = Animation::createWithSpriteFrames(frames, 0.1f);
auto animate = Animate::create(animation);
sprite->runAction(RepeatForever::create(animate));
`;

// 测试查询
const testQueries = [
    "精灵图集 sprite sheet 角色动画 帧动画",
    "sprite animation",
    "SpriteFrame",
    "Animation"
];

// 测试每个查询的相关性分数
testQueries.forEach((query, index) => {
    console.log(`📝 测试查询 ${index + 1}: "${query}"`);
    
    const relevanceScore = calculateRelevance(testContent, query);
    const searchTerms = extractSearchTerms(query);
    
    console.log(`   📊 相关性分数: ${relevanceScore}`);
    console.log(`   🔍 搜索词汇: ${searchTerms.join(', ')}`);
    console.log(`   ✅ 游戏引擎相关: ${isGameEngineRelated(query)}`);
    console.log(`   🎮 精灵动画相关: ${isSpriteAnimationRelated(query)}`);
    console.log('');
});

console.log("✅ 相关性测试完成");
