/**
 * 并发控制和超时管理工具
 * 提供统一的并发控制、超时管理和错误处理
 */

export interface ConcurrencyOptions {
  maxConcurrent?: number;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export class ConcurrencyManager {
  private static readonly DEFAULT_OPTIONS: Required<ConcurrencyOptions> = {
    maxConcurrent: 3,
    timeout: 5000,
    retries: 2,
    retryDelay: 1000
  };

  /**
   * 并发执行任务，限制并发数和超时时间
   */
  static async executeConcurrent<T>(
    tasks: (() => Promise<T>)[],
    options: ConcurrencyOptions = {}
  ): Promise<T[]> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const results: T[] = [];
    const executing: Promise<void>[] = [];

    for (const task of tasks) {
      const promise = this.executeWithTimeout(task, opts)
        .then(result => {
          if (result !== null) {
            results.push(result);
          }
        })
        .catch(error => {
          console.log(`⚠️ 任务执行失败: ${error.message}`);
        })
        .finally(() => {
          executing.splice(executing.indexOf(promise), 1);
        });

      executing.push(promise);

      if (executing.length >= opts.maxConcurrent) {
        await Promise.race(executing);
      }
    }

    await Promise.allSettled(executing);
    return results;
  }

  /**
   * 执行单个任务，带超时控制和重试机制
   */
  static async executeWithTimeout<T>(
    task: () => Promise<T>,
    options: ConcurrencyOptions = {}
  ): Promise<T | null> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    
    for (let attempt = 0; attempt <= opts.retries; attempt++) {
      try {
        const result = await Promise.race([
          task(),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Operation timeout')), opts.timeout)
          )
        ]);
        return result;
      } catch (error) {
        if (attempt === opts.retries) {
          throw error;
        }
        
        // 如果不是最后一次尝试，等待重试
        if (opts.retryDelay > 0) {
          await this.delay(opts.retryDelay);
        }
      }
    }
    
    return null;
  }

  /**
   * 批量处理任务，自动分批和错误处理
   */
  static async processBatch<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    options: ConcurrencyOptions = {}
  ): Promise<R[]> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const results: R[] = [];
    
    // 分批处理
    for (let i = 0; i < items.length; i += opts.maxConcurrent) {
      const batch = items.slice(i, i + opts.maxConcurrent);
      const batchTasks = batch.map(item => () => processor(item));
      
      const batchResults = await this.executeConcurrent(batchTasks, opts);
      results.push(...batchResults);
    }
    
    return results;
  }

  /**
   * 延迟执行
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建带超时的Promise
   */
  static withTimeout<T>(
    promise: Promise<T>,
    timeout: number,
    errorMessage: string = 'Operation timeout'
  ): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(errorMessage)), timeout)
      )
    ]);
  }

  /**
   * 重试执行函数
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxAttempts) {
          throw lastError;
        }
        
        if (delay > 0) {
          await this.delay(delay);
        }
      }
    }
    
    throw lastError!;
  }
}